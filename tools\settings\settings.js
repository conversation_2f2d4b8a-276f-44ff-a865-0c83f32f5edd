// Settings Tool JavaScript
(function() {
    // Initialize settings when loaded
    initializeSettings();

    function initializeSettings() {
        // Load saved settings
        loadSettings();

        // Set up event listeners
        setupEventListeners();

        console.log('Settings tool loaded');
    }

    function setupEventListeners() {
        // Theme selector
        const themeSelect = document.getElementById('theme-select');
        if (themeSelect) {
            themeSelect.addEventListener('change', handleThemeChange);
        }

        // Auto-save settings checkbox
        const autoSaveCheckbox = document.getElementById('auto-save-settings');
        if (autoSaveCheckbox) {
            autoSaveCheckbox.addEventListener('change', handleAutoSaveChange);
        }

        // Show tooltips checkbox
        const tooltipsCheckbox = document.getElementById('show-tooltips');
        if (tooltipsCheckbox) {
            tooltipsCheckbox.addEventListener('change', handleTooltipsChange);
        }

        // Reset settings button
        const resetButton = document.getElementById('reset-settings');
        if (resetButton) {
            resetButton.addEventListener('click', handleResetSettings);
        }

        // Clear cache button
        const clearCacheBtn = document.getElementById('clear-cache');
        if (clearCacheBtn) {
            clearCacheBtn.addEventListener('click', handleClearCache);
        }

        // LLM fields
        const provider = document.getElementById('llm-provider');
        const apiKey = document.getElementById('llm-api-key');
        const baseUrl = document.getElementById('llm-base-url');
        const model = document.getElementById('llm-model');
        const visionModel = document.getElementById('llm-vision-model');
        [provider, apiKey, baseUrl, model, visionModel].forEach(el => {
            if (el) el.addEventListener('change', handleLLMChange);
            if (el) el.addEventListener('input', handleLLMChange);
        });
    }

    function handleThemeChange(event) {
        const selectedTheme = event.target.value;
        applyTheme(selectedTheme);
        saveSettings();

        // Show feedback
        showSettingsFeedback('Theme changed successfully!');
    }

    function applyTheme(theme) {
        // Apply theme to document root
        if (theme === 'dark') {
            document.documentElement.removeAttribute('data-theme');
        } else {
            document.documentElement.setAttribute('data-theme', theme);
        }
    }

    function handleAutoSaveChange(event) {
        const isEnabled = event.target.checked;
        saveSettings();
        showSettingsFeedback(`Auto-save ${isEnabled ? 'enabled' : 'disabled'}`);
    }

    function handleTooltipsChange(event) {
        const isEnabled = event.target.checked;
        saveSettings();
        showSettingsFeedback(`Tooltips ${isEnabled ? 'enabled' : 'disabled'}`);
    }

    function handleResetSettings() {
        if (confirm('Are you sure you want to reset all settings to their defaults? This action cannot be undone.')) {
            // Reset to defaults
            const defaults = {
                theme: 'dark',
                autoSave: false,
                showTooltips: true,
                llm: { provider: 'openai', apiKey: '', baseUrl: '', model: '', visionModel: '' }
            };

            // Apply defaults to UI
            document.getElementById('theme-select').value = defaults.theme;
            document.getElementById('auto-save-settings').checked = defaults.autoSave;
            document.getElementById('show-tooltips').checked = defaults.showTooltips;
            if (document.getElementById('llm-provider')) document.getElementById('llm-provider').value = defaults.llm.provider;
            if (document.getElementById('llm-api-key')) document.getElementById('llm-api-key').value = defaults.llm.apiKey;
            if (document.getElementById('llm-base-url')) document.getElementById('llm-base-url').value = defaults.llm.baseUrl;
            if (document.getElementById('llm-model')) document.getElementById('llm-model').value = defaults.llm.model;
            if (document.getElementById('llm-vision-model')) document.getElementById('llm-vision-model').value = defaults.llm.visionModel;

            // Apply theme
            applyTheme(defaults.theme);


            // Save defaults
            localStorage.setItem('comfyui-helper-settings', JSON.stringify(defaults));

            showSettingsFeedback('Settings reset to defaults');
        }
    }

    function saveSettings() {
        const llm = {
            provider: document.getElementById('llm-provider')?.value || 'openai',
            apiKey: document.getElementById('llm-api-key')?.value || '',
            baseUrl: document.getElementById('llm-base-url')?.value || '',
            model: document.getElementById('llm-model')?.value || '',
            visionModel: document.getElementById('llm-vision-model')?.value || ''
        };
        const settings = {
            theme: document.getElementById('theme-select')?.value || 'dark',
            autoSave: document.getElementById('auto-save-settings')?.checked || false,
            showTooltips: document.getElementById('show-tooltips')?.checked || true,
            llm
        };

        localStorage.setItem('comfyui-helper-settings', JSON.stringify(settings));
    }

    function loadSettings() {
        try {
            const savedSettings = localStorage.getItem('comfyui-helper-settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);

                // Apply saved theme
                if (settings.theme) {
                    const themeSelect = document.getElementById('theme-select');
                    if (themeSelect) {
                        themeSelect.value = settings.theme;
                        applyTheme(settings.theme);
                    }
                }

                // Apply other settings
                const autoSaveCheckbox = document.getElementById('auto-save-settings');
                if (autoSaveCheckbox && typeof settings.autoSave === 'boolean') {
                    autoSaveCheckbox.checked = settings.autoSave;
                }

                const tooltipsCheckbox = document.getElementById('show-tooltips');
                if (tooltipsCheckbox && typeof settings.showTooltips === 'boolean') {
                    tooltipsCheckbox.checked = settings.showTooltips;
                }

                // Apply LLM settings
                if (settings.llm) {
                    const p = document.getElementById('llm-provider');
                    const k = document.getElementById('llm-api-key');
                    const u = document.getElementById('llm-base-url');
                    const m = document.getElementById('llm-model');
                    const vm = document.getElementById('llm-vision-model');
                    if (p && settings.llm.provider) p.value = settings.llm.provider;
                    if (k && typeof settings.llm.apiKey === 'string') k.value = settings.llm.apiKey;
                    if (u && typeof settings.llm.baseUrl === 'string') u.value = settings.llm.baseUrl;
                    if (m && typeof settings.llm.model === 'string') m.value = settings.llm.model;
                    if (vm && typeof settings.llm.visionModel === 'string') vm.value = settings.llm.visionModel;
                }
            }
        } catch (error) {
            console.warn('Failed to load settings:', error);
        }
    }

    function handleLLMChange(){
        saveSettings();
        showSettingsFeedback('AI model settings saved');
    }

    function showSettingsFeedback(message) {
        // Create or update feedback element
        let feedback = document.querySelector('.settings-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'settings-feedback';
            feedback.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: var(--success-color);
                color: white;
                padding: 0.75rem 1rem;
                border-radius: 8px;
                font-size: 0.9rem;
                font-weight: 500;
                z-index: 1000;
                opacity: 0;
                transform: translateY(-10px);
                transition: all 0.3s ease;
            `;
            document.body.appendChild(feedback);
        }

        feedback.textContent = message;

        // Show feedback
        requestAnimationFrame(() => {
            feedback.style.opacity = '1';
            feedback.style.transform = 'translateY(0)';
        });

        // Hide feedback after 3 seconds
        setTimeout(() => {
            feedback.style.opacity = '0';
            feedback.style.transform = 'translateY(-10px)';

            setTimeout(() => {
                if (feedback.parentNode) {
                    feedback.parentNode.removeChild(feedback);
                }
            }, 300);
        }, 3000);
    }

    // Initialize theme on page load (for when settings tool isn't the first loaded)
    function initializeThemeOnLoad() {
        try {
            const savedSettings = localStorage.getItem('comfyui-helper-settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                if (settings.theme) {
                    applyTheme(settings.theme);
                }
            }
        } catch (error) {
            console.warn('Failed to initialize theme:', error);
        }
    }


    // Clear all caches/storage for this app
    async function handleClearCache() {
        if (!confirm('This will remove all locally stored data for this app (settings, caches, and databases). Continue?')) return;
        try {
            localStorage.clear();
            if (window.sessionStorage) sessionStorage.clear();
            if (window.caches && caches.keys) {
                const names = await caches.keys();
                await Promise.all(names.map(n => caches.delete(n)));
            }
            if (window.indexedDB && indexedDB.databases) {
                const dbs = await indexedDB.databases();
                await Promise.all((dbs || []).map(db => db?.name ? new Promise((resolve) => {
                    const req = indexedDB.deleteDatabase(db.name);
                    req.onsuccess = req.onerror = req.onblocked = () => resolve();
                }) : Promise.resolve()));
            }
            showSettingsFeedback('Local cache cleared');
        } catch (e) {
            console.warn('Failed clearing cache:', e);
            showSettingsFeedback('Some cache items could not be cleared');
        }
        setTimeout(() => location.reload(), 400);
    }

    // Call theme initialization immediately
    initializeThemeOnLoad();
})();
