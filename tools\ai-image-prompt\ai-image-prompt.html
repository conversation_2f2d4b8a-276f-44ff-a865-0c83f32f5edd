<div class="dashboard-view active">
    <h2>AI Image Prompt Generation</h2>
    <p class="view-description">Convert images to prompts, enhance short ideas, or ask AI to describe and analyze images.</p>

    <div class="tab-switcher" role="tablist">
        <button class="tab-button active" data-tab="img2prompt" role="tab" aria-selected="true">Image to Prompt</button>
        <button class="tab-button" data-tab="magic" role="tab" aria-selected="false">Magic Enhance</button>
        <button class="tab-button" data-tab="describe" role="tab" aria-selected="false">AI Describe</button>
    </div>

        <!-- Global Output Length Control -->
        <div class="section grid-2" aria-label="Output length settings">
            <div>
                <label for="word-count" class="setting-label">Target length (words)</label>
                <input type="range" id="word-count" min="10" max="400" step="10" value="100" aria-valuemin="10" aria-valuemax="400" aria-valuenow="100" aria-label="Target words">
            </div>
            <div class="hint">Approximate maximum words: <strong><span id="word-count-value">100</span></strong></div>
        </div>

    <!-- Image to Prompt -->
    <section class="tab-panel" id="tab-img2prompt" role="tabpanel">
        <div class="section">
            <label class="file-upload">
                <input type="file" id="img2prompt-file" accept="image/*">
                <span>Upload Image</span>
            </label>
            <div class="preview-row">
                <img id="img2prompt-preview" class="image-preview" alt="Preview" />
            </div>
        </div>
        <div class="section grid-2">
            <div>
                <label for="img2prompt-model" class="setting-label">Prompt Model</label>
                <select id="img2prompt-model" class="setting-select">
                    <option value="general">General Image Prompt</option>
                    <option value="flux">Flux</option>
                    <option value="sd">Stable Diffusion</option>
                </select>
            </div>
            <div class="hint">Uses your configured AI provider in Settings</div>
        </div>
        <div class="actions">
            <button id="img2prompt-generate" class="action-button">Generate Prompt</button>
            <button id="img2prompt-copy" class="action-button secondary">Copy</button>
        </div>
        <textarea id="img2prompt-output" class="output" rows="6" placeholder="Generated prompt will appear here..."></textarea>
    </section>

    <!-- Magic Enhance -->
    <section class="tab-panel hidden" id="tab-magic" role="tabpanel">
        <div class="section">
            <label for="magic-input">Your idea (short text)</label>
            <textarea id="magic-input" rows="3" placeholder="e.g., a cozy cabin in the woods"></textarea>
        </div>
        <div class="section grid-2">
            <div>
                <label for="magic-model" class="setting-label">Target Prompt Style</label>
                <select id="magic-model" class="setting-select">
                    <option value="general">General Image Prompt</option>
                    <option value="flux">Flux</option>
                    <option value="sd">Stable Diffusion</option>
                </select>
            </div>
            <div class="hint">LLM will expand and optimize into the selected style</div>
        </div>
        <div class="actions">
            <button id="magic-generate" class="action-button">Enhance</button>
            <button id="magic-copy" class="action-button secondary">Copy</button>
        </div>
        <textarea id="magic-output" class="output" rows="6" placeholder="Enhanced prompt will appear here..."></textarea>
    </section>

    <!-- AI Describe -->
    <section class="tab-panel hidden" id="tab-describe" role="tabpanel">
        <div class="section">
            <label class="file-upload">
                <input type="file" id="describe-file" accept="image/*">
                <span>Upload Image</span>
            </label>
            <div class="preview-row">
                <img id="describe-preview" class="image-preview" alt="Preview" />
            </div>
        </div>
        <div class="section grid-2">
            <div>
                <label for="describe-option" class="setting-label">Image Description Options</label>
                <select id="describe-option" class="setting-select">
                    <option value="detail">Describe Image In Detail</option>
                    <option value="brief">Describe Image Briefly</option>
                    <option value="person">Describe The Person</option>
                    <option value="objects">Recognize Objects</option>
                    <option value="art">Analyze Art Style</option>
                    <option value="ocr">Extract Text From Image</option>
                    <option value="general">General Image Prompt</option>
                    <option value="flux">Flux Prompt</option>
                    <option value="sd">Stable Diffusion Prompt</option>
                    <option value="custom">Custom Question</option>
                </select>
            </div>
            <div id="custom-question-wrap" class="hidden">
                <label for="custom-question" class="setting-label">Your question</label>
                <input id="custom-question" type="text" placeholder="e.g., What is this person doing?" />
            </div>
        </div>
        <div class="actions">
            <button id="describe-generate" class="action-button">Generate</button>
            <button id="describe-copy" class="action-button secondary">Copy</button>
        </div>
        <textarea id="describe-output" class="output" rows="8" placeholder="Result will appear here..."></textarea>
        <div class="note">Note: Image understanding requires a vision-capable model on your selected provider.</div>
    </section>
</div>

