<div class="dashboard-view active">
    <h2>Settings</h2>
    <p class="view-description">Configure your dashboard preferences and tool settings.</p>

    <div class="settings-section">
        <h3>
            <svg class="section-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="5"/>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
            </svg>
            Appearance
        </h3>
        <div class="setting-group">
            <label for="theme-select" class="setting-label">Theme</label>
            <p class="setting-description">Choose your preferred color theme for the dashboard</p>
            <select id="theme-select" class="setting-select">
                <option value="dark">Dark (Default)</option>
                <option value="light">Light</option>
                <option value="ocean">Ocean</option>
                <option value="forest">Forest</option>
                <option value="purple">Purple</option>
            </select>
        </div>
    </div>

    <div class="settings-section">
        <h3>
            <svg class="section-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            Preferences
        </h3>
        <div class="setting-group">
            <label class="setting-checkbox">
                <input type="checkbox" id="auto-save-settings">
                <span class="checkmark"></span>
                <div class="checkbox-content">
                    <span class="checkbox-label">Auto-save settings</span>
                    <span class="checkbox-description">Automatically save your preferences</span>
                </div>
            </label>
        </div>
        <div class="setting-group">
            <label class="setting-checkbox">
                <input type="checkbox" id="show-tooltips" checked>
                <span class="checkmark"></span>
                <div class="checkbox-content">
                    <span class="checkbox-label">Show tooltips</span>
                    <span class="checkbox-description">Display helpful tooltips throughout the interface</span>
                </div>
            </label>
        </div>
    </div>

    <div class="settings-section">
        <h3>
            <svg class="section-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 12l2 2 4-4"/>
                <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
                <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
                <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3"/>
                <path d="M12 21c0-1 1-3 3-3s3 2 3 3-1 3-3 3-3-2-3-3"/>
            </svg>
            Actions
        </h3>
        <div class="setting-group">
            <button id="reset-settings" class="action-button secondary">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M1 4v6h6"/>
                    <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                </svg>
                Reset to Defaults
            </button>
            <button id="clear-cache" class="action-button danger" title="Clear local storage, caches, and IndexedDB for this app">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="3 6 5 6 21 6"/>
                    <path d="M19 6l-1 14a2 2 0 0 1-2 2H8a2 2 0 0 1-2-2L5 6"/>
                    <path d="M10 11v6"/>
                    <path d="M14 11v6"/>
                    <path d="M9 6V4a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v2"/>
                </svg>
                Clear Cache
            </button>

        </div>
    </div>

    <div class="settings-section">
        <h3>
            <svg class="section-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2l3 7h7l-5.5 4 2 7-6.5-4.5L5.5 20l2-7L2 9h7l3-7z"/>
            </svg>
            AI Models
        </h3>
        <div class="setting-group">
            <label for="llm-provider" class="setting-label">Provider</label>
            <select id="llm-provider" class="setting-select">
                <option value="openai">OpenAI</option>
                <option value="gemini">Gemini</option>
                <option value="groq">Groq</option>
                <option value="ollama">Ollama (local)</option>
                <option value="lmstudio">LM Studio (local)</option>
            </select>
        </div>
        <div class="setting-group">
            <label for="llm-api-key" class="setting-label">API Key (if required)</label>
            <input type="password" id="llm-api-key" placeholder="Enter API key for selected provider" />
        </div>
            <div class="settings-warning" role="note" aria-live="polite">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"/>
                    <path d="M12 16v-4"/>
                    <path d="M12 8h.01"/>
                </svg>
                <span>Your API keys are stored locally in your browser on this device. Do not share passwords or export settings if others can access them.</span>
            </div>

        <div class="setting-group">
            <label for="llm-base-url" class="setting-label">Base URL (optional)</label>
            <input type="text" id="llm-base-url" placeholder="e.g., http://localhost:1234 for local providers" />
        </div>
        <div class="grid-2">
            <div class="setting-group">
                <label for="llm-model" class="setting-label">Default Text Model</label>
                <input type="text" id="llm-model" placeholder="e.g., gpt-4o-mini, gemini-1.5-flash, llama3.1" />
            </div>
            <div class="setting-group">
                <label for="llm-vision-model" class="setting-label">Vision Model</label>
                <input type="text" id="llm-vision-model" placeholder="e.g., gpt-4o, gemini-1.5-pro, (optional)" />
            </div>
        </div>
        <p class="settings-info">These settings are used by AI Image Prompt tool.</p>
    </div>

    <div class="settings-footer">
        <p class="settings-info">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"/>
                <path d="M12 16v-4"/>
                <path d="M12 8h.01"/>
            </svg>
            Settings are automatically saved to your browser's local storage
        </p>
    </div>
</div>
