/* Default Theme (Dark) */
:root {
    --bg-color: #1a1b26;
    --surface-color: #24283b;
    --primary-color: #7aa2f7;
    --text-color: #c0caf5;
    --subtle-text-color: #565f89;
    --border-color: #3b4261;
    --success-color: #9ece6a;
    --error-color: #f7768e;
    --warning-color: #e0af68;
    --sidebar-width: 260px;
    --sidebar-width-collapsed: 90px;
    /* Add responsive-friendly tokens */
    --card-bg: var(--surface-color);
    --muted-text-color: var(--subtle-text-color);
}

/* Light Theme */
[data-theme="light"] {
    --bg-color: #ffffff;
    --surface-color: #f8f9fa;
    --primary-color: #0066cc;
    --text-color: #2d3748;
    --subtle-text-color: #718096;
    --border-color: #e2e8f0;
    --success-color: #38a169;
    --error-color: #e53e3e;
    --warning-color: #d69e2e;
}

/* Ocean Theme */
[data-theme="ocean"] {
    --bg-color: #0f172a;
    --surface-color: #1e293b;
    --primary-color: #0ea5e9;
    --text-color: #e2e8f0;
    --subtle-text-color: #64748b;
    --border-color: #334155;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
}

/* Forest Theme */
[data-theme="forest"] {
    --bg-color: #0f1419;
    --surface-color: #1a2332;
    --primary-color: #7fb069;
    --text-color: #d4d4aa;
    --subtle-text-color: #8a9a5b;
    --border-color: #2d3748;
    --success-color: #68d391;
    --error-color: #fc8181;
    --warning-color: #f6ad55;
}

/* Purple Theme */
[data-theme="purple"] {
    --bg-color: #1a1625;
    --surface-color: #2d1b69;
    --primary-color: #9f7aea;
    --text-color: #e9d8fd;
    --subtle-text-color: #b794f6;
    --border-color: #553c9a;
    --success-color: #68d391;
    --error-color: #fc8181;
    --warning-color: #f6ad55;
}

* { box-sizing: border-box; margin: 0; padding: 0; }
body { font-family: 'Poppins', sans-serif; background-color: var(--bg-color); color: var(--text-color); display: flex; }

/* --- Sidebar (no changes) --- */
#sidebar { width: var(--sidebar-width); height: 100vh; background-color: var(--surface-color); border-right: 1px solid var(--border-color); display: flex; flex-direction: column; padding: 1.5rem 0; transition: width 0.3s ease; }
.sidebar-header { padding: 0 1.5rem 1.5rem 1.5rem; text-align: center; border-bottom: 1px solid var(--border-color); margin-bottom: 1rem; }
.sidebar-header h1 { font-size: 1.5rem; color: #fff; }
.sidebar-nav { list-style: none; flex-grow: 1; }
.nav-item a { display: flex; align-items: center; padding: 1rem 1.75rem; text-decoration: none; color: var(--text-color); font-weight: 500; transition: all 0.3s; white-space: nowrap; overflow: hidden; }
.nav-item a:hover { background-color: rgba(122, 162, 247, 0.1); }
.nav-item.active a { background-color: var(--primary-color); color: var(--bg-color); font-weight: 600; }
.nav-icon { margin-right: 1rem; width: 24px; height: 24px; flex-shrink: 0; }

/* --- Main Content --- */
#main-content { flex-grow: 1; height: 100vh; overflow-y: auto; padding: 2.5rem; }
.dashboard-view { display: none; }
.dashboard-view.active { display: block; animation: fadeIn 0.5s ease; }
@keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
.dashboard-view h2 { margin-bottom: 0.5rem; }
.view-description { color: var(--subtle-text-color); margin-bottom: 2rem; }

/* --- Loading and Error States --- */
.loading {
    text-align: center;
    padding: 3rem;
    color: var(--subtle-text-color);
    font-size: 1.1rem;
}

.error {
    text-align: center;
    padding: 3rem;
    color: var(--error-color);
    font-size: 1.1rem;
    background-color: rgba(247, 118, 142, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(247, 118, 142, 0.3);
}

/* --- Universal Action Button Style --- */
.action-button { display: flex; align-items: center; justify-content: center; gap: 0.75rem; width: 100%; padding: 1rem; background: var(--primary-color); color: #fff; border: none; border-radius: 8px; font-size: 1.1rem; font-weight: 500; cursor: pointer; transition: all 0.3s ease; }
.action-button:hover { background: #6c8eef; transform: translateY(-2px); box-shadow: 0 6px 20px rgba(122, 162, 247, 0.3); }

/* --- Responsive Design (Updated) --- */
@media (max-width: 1024px) {
    #sidebar { width: var(--sidebar-width-collapsed); }
    .sidebar-header h1, .nav-text { display: none; }
    .nav-item a { justify-content: center; padding: 1.25rem 0; }
    .nav-icon { margin-right: 0; }
}
@media (max-width: 768px) {
    body { display: block; }
    #sidebar { width: 100%; height: auto; flex-direction: row; border-right: none; border-bottom: 1px solid var(--border-color); }
    .sidebar-header { display: none; }
    .sidebar-nav { display: flex; justify-content: space-around; width: 100%; }
    #main-content { height: auto; padding: 1.5rem; }
}

/* --- Global responsive helpers --- */
img, video { max-width: 100%; height: auto; }

/* Tighten small-screen layout */
@media (max-width: 768px) {
    .sidebar-nav { overflow-x: auto; -webkit-overflow-scrolling: touch; padding: 0.25rem; }
}

@media (max-width: 480px) {
    #main-content { padding: 1rem; }
    .nav-item a { padding: 0.75rem 0.5rem; }
    .nav-icon { width: 20px; height: 20px; }
}