.tab-switcher{display:flex;gap:.5rem;margin:1rem 0}.tab-button{padding:.5rem .75rem;border:1px solid var(--border-color);background:transparent;border-radius:.5rem;color:var(--text-color);cursor:pointer}.tab-button.active{background:var(--card-bg);border-color:var(--primary-color);color:var(--primary-color)}.tab-panel{margin-top:1rem}.hidden{display:none}.section{margin:.75rem 0}.grid-2{display:grid;grid-template-columns:1fr 1fr;gap:1rem;align-items:end}.file-upload{display:inline-flex;align-items:center;gap:.5rem;border:1px dashed var(--border-color);padding:.5rem .75rem;border-radius:.5rem;color:var(--text-color);cursor:pointer}.file-upload input{display:none}.preview-row{margin-top:.5rem}.image-preview{max-width:100%;max-height:220px;border-radius:.5rem;border:1px solid var(--border-color)}.actions{display:flex;gap:.5rem;margin:.5rem 0}.output{width:100%;border:1px solid var(--border-color);border-radius:.5rem;padding:.75rem;background:var(--card-bg);color:var(--text-color)}.hint{font-size:.85rem;color:var(--muted-text-color);align-self:center}.note{margin-top:.5rem;font-size:.85rem;color:var(--muted-text-color)}input[type="text"],select,textarea{width:100%;border:1px solid var(--border-color);border-radius:.5rem;padding:.5rem;background:var(--bg-color);color:var(--text-color)}

/* Neon glow during loading */
@keyframes neonPulse{0%{box-shadow:0 0 0 2px rgba(0,255,255,.35),0 0 14px 2px rgba(0,255,255,.5),0 0 28px 8px rgba(255,0,255,.35)}50%{box-shadow:0 0 0 2px rgba(255,0,255,.35),0 0 16px 4px rgba(255,0,255,.55),0 0 34px 10px rgba(0,255,255,.4)}100%{box-shadow:0 0 0 2px rgba(0,255,255,.35),0 0 14px 2px rgba(0,255,255,.5),0 0 28px 8px rgba(255,0,255,.35)}}
.output.glow-loading{border-color:transparent;animation:neonPulse 1.4s ease-in-out infinite;background:linear-gradient(var(--card-bg),var(--card-bg)) padding-box,linear-gradient(135deg,rgba(0,255,255,.5),rgba(255,0,255,.5)) border-box;border:2px solid transparent}

