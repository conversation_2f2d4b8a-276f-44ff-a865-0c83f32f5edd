// Wait for the DOM to be fully loaded before running scripts
document.addEventListener('DOMContentLoaded', function() {

    // --- Initialize Dashboard ---
    initializeTheme();
    initializeNavigation();

    // Load the default tool (prompt-generator)
    loadTool('prompt-generator');

});

// --- MODULAR DASHBOARD NAVIGATION LOGIC ---
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.sidebar-nav a');

    navLinks.forEach(link => {
        link.addEventListener('click', function(event) {
            event.preventDefault();
            const targetTool = this.getAttribute('data-target');

            // Update active navigation state
            navLinks.forEach(navLink => navLink.parentElement.classList.remove('active'));
            this.parentElement.classList.add('active');

            // Load the selected tool
            loadTool(targetTool);
        });
    });
}

// --- TOOL TEMPLATES (Auto-generated from individual tool files) ---
const TOOL_TEMPLATES = {
    'ai-image-prompt': {
        html: `<div class="dashboard-view active">
    <h2>AI Image Prompt Generation</h2>
    <p class="view-description">Convert images to prompts, enhance short ideas, or ask AI to describe and analyze images.</p>

    <div class="tab-switcher" role="tablist">
        <button class="tab-button active" data-tab="img2prompt" role="tab" aria-selected="true">Image to Prompt</button>
        <button class="tab-button" data-tab="magic" role="tab" aria-selected="false">Magic Enhance</button>
        <button class="tab-button" data-tab="describe" role="tab" aria-selected="false">AI Describe</button>
    </div>

    <!-- Global Output Length Control -->
    <div class="section grid-2" aria-label="Output length settings">
        <div>
            <label for="word-count" class="setting-label">Target length (words)</label>
            <input type="range" id="word-count" min="10" max="400" step="10" value="100" aria-valuemin="10" aria-valuemax="400" aria-valuenow="100" aria-label="Target words">
        </div>
        <div class="hint">Approximate maximum words: <strong><span id="word-count-value">100</span></strong></div>
    </div>


    <!-- Image to Prompt -->
    <section class="tab-panel" id="tab-img2prompt" role="tabpanel">
        <div class="section">
            <label class="file-upload">
                <input type="file" id="img2prompt-file" accept="image/*">
                <span>Upload Image</span>
            </label>
            <div class="preview-row">
                <img id="img2prompt-preview" class="image-preview" alt="Preview" />
            </div>
        </div>
        <div class="section grid-2">
            <div>
                <label for="img2prompt-model" class="setting-label">Prompt Model</label>
                <select id="img2prompt-model" class="setting-select">
                    <option value="general">General Image Prompt</option>
                    <option value="flux">Flux</option>
                    <option value="sd">Stable Diffusion</option>
                </select>
            </div>
            <div class="hint">Uses your configured AI provider in Settings</div>
        </div>
        <div class="actions">
            <button id="img2prompt-generate" class="action-button">Generate Prompt</button>
            <button id="img2prompt-copy" class="action-button secondary">Copy</button>
        </div>
        <textarea id="img2prompt-output" class="output" rows="6" placeholder="Generated prompt will appear here..."></textarea>
    </section>

    <!-- Magic Enhance -->
    <section class="tab-panel hidden" id="tab-magic" role="tabpanel">
        <div class="section">
            <label for="magic-input">Your idea (short text)</label>
            <textarea id="magic-input" rows="3" placeholder="e.g., a cozy cabin in the woods"></textarea>
        </div>
        <div class="section grid-2">
            <div>
                <label for="magic-model" class="setting-label">Target Prompt Style</label>
                <select id="magic-model" class="setting-select">
                    <option value="general">General Image Prompt</option>
                    <option value="flux">Flux</option>
                    <option value="sd">Stable Diffusion</option>
                </select>
            </div>
            <div class="hint">LLM will expand and optimize into the selected style</div>
        </div>
        <div class="actions">
            <button id="magic-generate" class="action-button">Enhance</button>
            <button id="magic-copy" class="action-button secondary">Copy</button>
        </div>
        <textarea id="magic-output" class="output" rows="6" placeholder="Enhanced prompt will appear here..."></textarea>
    </section>

    <!-- AI Describe -->
    <section class="tab-panel hidden" id="tab-describe" role="tabpanel">
        <div class="section">
            <label class="file-upload">
                <input type="file" id="describe-file" accept="image/*">
                <span>Upload Image</span>
            </label>
            <div class="preview-row">
                <img id="describe-preview" class="image-preview" alt="Preview" />
            </div>
        </div>
        <div class="section grid-2">
            <div>
                <label for="describe-option" class="setting-label">Image Description Options</label>
                <select id="describe-option" class="setting-select">
                    <option value="detail">Describe Image In Detail</option>
                    <option value="brief">Describe Image Briefly</option>
                    <option value="person">Describe The Person</option>
                    <option value="objects">Recognize Objects</option>
                    <option value="art">Analyze Art Style</option>
                    <option value="ocr">Extract Text From Image</option>
                    <option value="general">General Image Prompt</option>
                    <option value="flux">Flux Prompt</option>
                    <option value="sd">Stable Diffusion Prompt</option>
                    <option value="custom">Custom Question</option>
                </select>
            </div>
            <div id="custom-question-wrap" class="hidden">
                <label for="custom-question" class="setting-label">Your question</label>
                <input id="custom-question" type="text" placeholder="e.g., What is this person doing?" />
            </div>
        </div>
        <div class="actions">
            <button id="describe-generate" class="action-button">Generate</button>
            <button id="describe-copy" class="action-button secondary">Copy</button>
        </div>
        <textarea id="describe-output" class="output" rows="8" placeholder="Result will appear here..."></textarea>
        <div class="note">Note: Image understanding requires a vision-capable model on your selected provider.</div>
    </section>
</div>`,
        css: `.tab-switcher{display:flex;gap:.5rem;margin:1rem 0;flex-wrap:wrap}.tab-button{padding:.5rem .75rem;border:1px solid var(--border-color);background:transparent;border-radius:.5rem;color:var(--text-color);cursor:pointer}.tab-button.active{background:var(--card-bg);border-color:var(--primary-color);color:var(--primary-color)}.tab-panel{margin-top:1rem}.hidden{display:none}.section{margin:.75rem 0}.grid-2{display:grid;grid-template-columns:repeat(auto-fit,minmax(220px,1fr));gap:1rem;align-items:end}.file-upload{display:inline-flex;align-items:center;gap:.5rem;border:1px dashed var(--border-color);padding:.5rem .75rem;border-radius:.5rem;color:var(--text-color);cursor:pointer}.file-upload input{display:none}.preview-row{margin-top:.5rem}.image-preview{max-width:100%;max-height:220px;border-radius:.5rem;border:1px solid var(--border-color)}.actions{display:flex;gap:.5rem;margin:.5rem 0;flex-wrap:wrap}.output{width:100%;border:1px solid var(--border-color);border-radius:.5rem;padding:.75rem;background:var(--card-bg);color:var(--text-color)}.hint{font-size:.85rem;color:var(--muted-text-color);align-self:center}.note{margin-top:.5rem;font-size:.85rem;color:var(--muted-text-color)}input[type="text"],select,textarea{width:100%;border:1px solid var(--border-color);border-radius:.5rem;padding:.5rem;background:var(--bg-color);color:var(--text-color)}

@media (max-width:768px){.image-preview{max-height:180px}}

/* Neon glow during loading */
@keyframes neonPulse{0%{box-shadow:0 0 0 2px rgba(0,255,255,.35),0 0 14px 2px rgba(0,255,255,.5),0 0 28px 8px rgba(255,0,255,.35)}50%{box-shadow:0 0 0 2px rgba(255,0,255,.35),0 0 16px 4px rgba(255,0,255,.55),0 0 34px 10px rgba(0,255,255,.4)}100%{box-shadow:0 0 0 2px rgba(0,255,255,.35),0 0 14px 2px rgba(0,255,255,.5),0 0 28px 8px rgba(255,0,255,.35)}}
.output.glow-loading{border-color:transparent;animation:neonPulse 1.4s ease-in-out infinite;background:linear-gradient(var(--card-bg),var(--card-bg)) padding-box,linear-gradient(135deg,rgba(0,255,255,.5),rgba(255,0,255,.5)) border-box;border:2px solid transparent}`,
        init: function() {
            initAIImagePromptTool();

    function initAIImagePromptTool() {

        document.querySelectorAll('.tab-button').forEach(btn => btn.addEventListener('click', onTabClick));

        setupPreview('img2prompt-file', 'img2prompt-preview');
        setupPreview('describe-file', 'describe-preview');

        onClick('img2prompt-generate', handleImg2Prompt);
        onClick('img2prompt-copy', () => copyFrom('img2prompt-output'));

        // Word count slider wiring
        const wordSlider = document.getElementById('word-count');
        const wordVal = document.getElementById('word-count-value');
        if (wordSlider && wordVal) {
            const update = () => {
                const v = parseInt(wordSlider.value, 10) || 100;
                wordVal.textContent = String(v);
                wordSlider.setAttribute('aria-valuenow', String(v));
            };
            update();
            wordSlider.addEventListener('input', update);
        }

        onClick('magic-generate', handleMagicEnhance);
        onClick('magic-copy', () => copyFrom('magic-output'));
        onClick('describe-generate', handleDescribe);
        onClick('describe-copy', () => copyFrom('describe-output'));

        const optionSel = document.getElementById('describe-option');
        if (optionSel) optionSel.addEventListener('change', () => {
            const isCustom = optionSel.value === 'custom';
            document.getElementById('custom-question-wrap')?.classList.toggle('hidden', !isCustom);
        });
    }

    function onTabClick(e) {
        const target = e.currentTarget;
        const tab = target.getAttribute('data-tab');
        document.querySelectorAll('.tab-button').forEach(b => b.classList.remove('active'));
        target.classList.add('active');
        document.querySelectorAll('.tab-panel').forEach(p => p.classList.add('hidden'));
        document.getElementById(`tab-${tab}`)?.classList.remove('hidden');
    }

    function onClick(id, fn) {
        const el = document.getElementById(id);
        if (el) el.addEventListener('click', fn);
    }

    function setupPreview(inputId, imgId) {
        const input = document.getElementById(inputId);
        const img = document.getElementById(imgId);
        if (!input || !img) return;
        input.addEventListener('change', () => {
            const file = input.files && input.files[0];
            if (!file) return;
            const reader = new FileReader();
            reader.onload = () => { img.src = reader.result; };
            reader.readAsDataURL(file);
        });
    }

    function getSettings() {
        try {
            const raw = localStorage.getItem('comfyui-helper-settings');
            return raw ? JSON.parse(raw) : {};
        } catch { return {}; }
    }

    function getLLMConfig() {
        const s = getSettings();
        return s.llm || { provider: 'openai', apiKey: '', baseUrl: '' };
    }

    function setOutput(id, text) {
        const el = document.getElementById(id);
        if (el) el.value = text || '';
    }

        // Global target word helpers
        function getTargetWords(){
            const el = document.getElementById('word-count');
            const v = parseInt(el && el.value, 10);
            if (!Number.isFinite(v)) return 100;
            return Math.min(400, Math.max(10, v));
        }

        function lengthInstruction(){
            const n = getTargetWords();
            return `Aim for no more than ${n} words. Shorter is fine if it improves quality.`;
        }


    async function handleImg2Prompt() {
        const modelStyle = document.getElementById('img2prompt-model')?.value || 'general';
        const fileInput = document.getElementById('img2prompt-file');
        const file = fileInput && fileInput.files && fileInput.files[0];
        if (!file) { alert('Please upload an image.'); return; }
        setImg2PromptLoading(true);
        try {
            const prompt = await callVisionPrompt(file, buildImg2PromptSystem(modelStyle), 'Describe this image for the target format.');
            setOutput('img2prompt-output', formatPromptByStyle(prompt, modelStyle));
        } catch (e) {
            setOutput('img2prompt-output', `Error: ${e.message || e}`);
        } finally {
            setImg2PromptLoading(false);
        }
    }

    async function handleMagicEnhance() {
        const idea = document.getElementById('magic-input')?.value?.trim();
        if (!idea) { alert('Please enter some text.'); return; }
        const modelStyle = document.getElementById('magic-model')?.value || 'general';
        setMagicLoading(true);
        try {
            const out = await callTextPrompt(buildMagicEnhanceSystem(modelStyle), `Idea: ${idea}`);

            const cleaned = cleanExpandedOutput(out);
            setOutput('magic-output', formatPromptByStyle(cleaned, modelStyle));
        } catch (e) {
            setOutput('magic-output', `Error: ${e.message || e}`);
        } finally {
            setMagicLoading(false);
        }
    }

    async function handleDescribe() {
        const fileInput = document.getElementById('describe-file');
        const file = fileInput && fileInput.files && fileInput.files[0];
        if (!file) { alert('Please upload an image.'); return; }
        const opt = document.getElementById('describe-option')?.value || 'detail';
        let user = '';
        switch(opt){
            case 'detail': user = 'Provide a thorough, multi-paragraph description of the image.'; break;
            case 'brief': user = 'Provide a concise 2-3 sentence description of the image.'; break;
            case 'person': user = 'Describe the person in the image, including clothing, pose, expression.'; break;
            case 'objects': user = 'List and briefly describe the main objects in the image.'; break;
            case 'art': user = 'Analyze the art style, medium, influences, composition, and color palette.'; break;
            case 'ocr': user = 'Extract all visible text (OCR) from the image.'; break;
            case 'general': user = 'Write a general image prompt suitable for text-to-image.'; break;
            case 'flux': user = 'Write a Flux-style prompt optimized for Flux models.'; break;
            case 'sd': user = 'Write a Stable Diffusion style prompt.'; break;
            case 'custom': user = document.getElementById('custom-question')?.value?.trim() || 'Describe the image.'; break;
        }
        setDescribeLoading(true);
        try {
            const system = buildDescribeSystem(opt);
            const out = await callVisionPrompt(file, system, user);
            const style = opt === 'flux' ? 'flux' : opt === 'sd' ? 'sd' : 'general';
            setOutput('describe-output', formatPromptByStyle(out, style));
        } catch (e) {
            setOutput('describe-output', `Error: ${e.message || e}`);
        } finally {
            setDescribeLoading(false);
        }
    }

    function buildImg2PromptSystem(style){
        let base;
        switch(style){
            case 'flux': base = 'You convert images into concise, high-signal Flux prompts (no extra commentary).'; break;
            case 'sd': base = 'You convert images into Stable Diffusion prompts with positive prompt only, no negative unless asked.'; break;
            default: base = 'You describe images as natural language prompts for text-to-image generation.'; break;
        }
        return base + ' ' + lengthInstruction();
    }

    function buildMagicEnhanceSystem(style){
        const common = 'You will be given a short idea. Produce ONE single best prompt ONLY. Do not include any explanations, headings, lists, markdown, or quotes. Output only the final prompt text.';
        switch(style){
            case 'flux': return common + ' Format as a concise Flux-style prompt focusing on subject, style, lighting, composition, camera. Keep one sentence if possible. ' + lengthInstruction();
            case 'sd': return common + ' Format as a Stable Diffusion positive prompt: comma-separated attributes; no negatives unless specified. ' + lengthInstruction();
            default: return common + ' Expand into a detailed, vivid natural language prompt suitable for text-to-image. ' + lengthInstruction();
        }
    }

    function buildDescribeSystem(opt){
        if (opt === 'ocr') return 'Extract text from the image exactly, preserving line breaks if visible.';
        if (opt === 'art') return 'Analyze art style, medium, influences, composition, and palette with specifics. ' + lengthInstruction();
        return 'Be precise and helpful about the image. ' + lengthInstruction();
    }

    function formatPromptByStyle(text, style){
        if (!text) return '';
        if (style === 'flux') {
            return text
                .replace(/\n+/g, ' ')
                .replace(/\s{2,}/g, ' ')
                .trim();
        }
        if (style === 'sd') {

            return text
                .replace(/\n+/g, ', ')
                .replace(/\s{2,}/g, ' ')
                .replace(/\,\s*\,/g, ', ')
                .trim();
        }
        return text.trim();
    }

    function cleanExpandedOutput(text){
        if (!text) return '';

        const firstLine = text.split(/\n+/).find(l => l.trim());
        let cleaned = text
            .replace(/^\s*(?:Idea|Original\s*Idea|Expanded\s*Prompt|Output)\s*:\s*/gim, '')
            .replace(/^\s*[\-*•]\s*/gm, '') // bullets
            .replace(/^#+\s*/gm, '')         // markdown headings
            .replace(/---+/g, ' ')            // separators
            .trim();

        const promptMatch = cleaned.match(/Prompt\s*:\s*([\s\S]*)/i);
        if (promptMatch) cleaned = promptMatch[1].trim();

        cleaned = cleaned.split(/\n\s*\n/)[0].trim();
        return cleaned;
    }

    async function callTextPrompt(system, user){
        const cfg = getLLMConfig();
        ensureProviderConfigured(cfg);
        if (cfg.provider === 'openai') {
            return callOpenAIChat(system, user, cfg);
        } else if (cfg.provider === 'gemini') {
            return callGeminiText(system, user, cfg);
        } else if (cfg.provider === 'groq') {
            return callGroqChat(system, user, cfg);
        } else if (cfg.provider === 'ollama' || cfg.provider === 'lmstudio') {
            return callLocalChat(system, user, cfg);
        } else {
            throw new Error('Unsupported provider');
        }
    }

    async function callVisionPrompt(file, system, user){
        const cfg = getLLMConfig();
        ensureProviderConfigured(cfg);
        if (cfg.provider === 'openai') {
            return callOpenAIVision(system, user, file, cfg);
        } else if (cfg.provider === 'gemini') {
            return callGeminiVision(system, user, file, cfg);
        } else if (cfg.provider === 'groq') {
            return callGroqVision(system, user, file, cfg);
        } else if (cfg.provider === 'ollama' || cfg.provider === 'lmstudio') {
            return callLocalVision(system, user, file, cfg);
        } else {
            throw new Error('Unsupported provider');
        }
    }

    function ensureProviderConfigured(cfg){
        if (!cfg || !cfg.provider) throw new Error('No provider selected in Settings');
        if (cfg.provider === 'ollama' || cfg.provider === 'lmstudio') {
            if (!cfg.baseUrl) throw new Error('Set base URL for local LLM in Settings');
        } else {
            if (!cfg.apiKey) throw new Error('Set API key for selected provider in Settings');
        }
    }

    async function callOpenAIChat(system, user, cfg){
        const url = (cfg.baseUrl || 'https://api.openai.com') + '/v1/chat/completions';
        const body = { model: cfg.model || 'gpt-4o-mini', messages: [ {role:'system', content: system}, {role:'user', content: user} ] };
        const res = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${cfg.apiKey}` }, body: JSON.stringify(body) });
        const j = await res.json();
        if (!res.ok) throw new Error(j.error?.message || 'OpenAI error');
        return j.choices?.[0]?.message?.content || '';
    }

    async function callOpenAIVision(system, user, file, cfg){
        const b64 = await fileToBase64(file);
        const url = (cfg.baseUrl || 'https://api.openai.com') + '/v1/chat/completions';
        const messages = [
            { role: 'system', content: system },
            { role: 'user', content: [ { type: 'text', text: user }, { type: 'image_url', image_url: { url: b64 } } ] }
        ];
        const body = { model: cfg.visionModel || cfg.model || 'gpt-4o-mini', messages };
        const res = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${cfg.apiKey}` }, body: JSON.stringify(body) });
        const j = await res.json();
        if (!res.ok) throw new Error(j.error?.message || 'OpenAI error');
        return j.choices?.[0]?.message?.content || '';
    }

    async function callGeminiText(system, user, cfg){
        const url = (cfg.baseUrl || 'https://generativelanguage.googleapis.com') + `/v1beta/models/${cfg.model || 'gemini-1.5-flash'}:generateContent?key=${cfg.apiKey}`;
        const body = { contents: [ { role: 'user', parts: [ { text: system + '\n' + user } ] } ] };
        const res = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(body) });
        const j = await res.json();
        if (!res.ok) throw new Error(j.error?.message || 'Gemini error');
        return j.candidates?.[0]?.content?.parts?.map(p=>p.text).join('') || '';
    }

    async function callGeminiVision(system, user, file, cfg){
        const b64 = await fileToBase64(file, true);
        const url = (cfg.baseUrl || 'https://generativelanguage.googleapis.com') + `/v1beta/models/${cfg.visionModel || cfg.model || 'gemini-1.5-flash'}:generateContent?key=${cfg.apiKey}`;
        const body = { contents: [ { role: 'user', parts: [ { text: system + '\n' + user }, { inline_data: { mime_type: file.type || 'image/png', data: b64.split(',')[1] } } ] } ] };
        const res = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(body) });
        const j = await res.json();
        if (!res.ok) throw new Error(j.error?.message || 'Gemini error');
        return j.candidates?.[0]?.content?.parts?.map(p=>p.text).join('') || '';
    }

    async function callGroqChat(system, user, cfg){
        const url = (cfg.baseUrl || 'https://api.groq.com') + '/openai/v1/chat/completions';
        const body = { model: cfg.model || 'llama-3.1-70b-versatile', messages: [ {role:'system', content: system}, {role:'user', content: user} ] };
        const res = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${cfg.apiKey}` }, body: JSON.stringify(body) });
        const j = await res.json();
        if (!res.ok) throw new Error(j.error?.message || 'Groq error');
        return j.choices?.[0]?.message?.content || '';
    }

    async function callGroqVision(system, user, file, cfg){


        return callGroqChat(system + ' If image is unsupported, answer based on general assumptions and note limitation.', user, cfg);
    }

    async function callLocalChat(system, user, cfg){

        const base = cfg.baseUrl?.replace(/\/$/, '') || 'http://localhost:11434';
        const url = (cfg.provider === 'ollama') ? `${base}/v1/chat/completions` : `${base}/v1/chat/completions`;
        const body = { model: cfg.model || 'llama3.1', messages: [ {role:'system', content: system}, {role:'user', content: user} ] };
        const headers = { 'Content-Type': 'application/json' };
        if (cfg.apiKey) headers['Authorization'] = `Bearer ${cfg.apiKey}`; // LMStudio may require none
        const res = await fetch(url, { method: 'POST', headers, body: JSON.stringify(body) });
        const j = await res.json();
        if (!res.ok) throw new Error(j.error?.message || 'Local LLM error');
        return j.choices?.[0]?.message?.content || '';
    }

    async function callLocalVision(system, user, file, cfg){

        return callLocalChat(system + ' Note: If vision is unsupported, answer generally and note limitation.', user, cfg);
    }

    async function copyFrom(id){
        const el = document.getElementById(id);
        if (!el) return;
        const text = ('value' in el) ? el.value : (el.textContent || '');
        try {
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(text);
            } else {
                const ta = document.createElement('textarea');
                ta.value = text;
                ta.setAttribute('readonly','');
                ta.style.cssText = 'position:fixed;left:-9999px;top:-9999px;';
                document.body.appendChild(ta);
                ta.select();
                document.execCommand('copy');
                document.body.removeChild(ta);
            }
            showToast('Copied to clipboard');
        } catch(err) {
            console.warn('Copy failed', err);
            showToast('Copy failed');
        }
    }

    function showToast(message){
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--success-color, #22c55e);
            color: #fff;
            padding: .6rem .9rem;
            border-radius: .5rem;
            box-shadow: 0 8px 24px rgba(0,0,0,.2);
            z-index: 10000;
            opacity: 0;
            transform: translateY(8px);
            transition: opacity .25s ease, transform .25s ease;
        `;
        document.body.appendChild(toast);
        requestAnimationFrame(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateY(0)';
        });
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateY(8px)';
            setTimeout(() => toast.remove(), 250);
        }, 1600);
    }

    function setDisabled(el, disabled){ if (!el) return; el.disabled = disabled; el.classList.toggle('is-loading', !!disabled); }

    function setImg2PromptLoading(loading){
        setDisabled(document.getElementById('img2prompt-generate'), loading);
        const out = document.getElementById('img2prompt-output');
        if (out) out.disabled = loading;
        if (out && loading) out.value = 'Generating...';
        if (out) out.placeholder = loading ? 'Generating…' : 'Generated prompt will appear here...';
        if (out) out.classList.toggle('glow-loading', loading);
    }

    function setMagicLoading(loading){
        setDisabled(document.getElementById('magic-generate'), loading);
        const out = document.getElementById('magic-output');
        if (out) out.disabled = loading;
        if (out && loading) out.value = 'Enhancing...';
        if (out) out.placeholder = loading ? 'Enhancing…' : 'Enhanced prompt will appear here...';
        if (out) out.classList.toggle('glow-loading', loading);
    }

    function setDescribeLoading(loading){
        setDisabled(document.getElementById('describe-generate'), loading);
        const out = document.getElementById('describe-output');
        if (out) out.disabled = loading;
        if (out && loading) out.value = 'Generating...';
        if (out) out.placeholder = loading ? 'Analyzing…' : 'Result will appear here...';
        if (out) out.classList.toggle('glow-loading', loading);
    }

    function fileToBase64(file){
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }
        }
    },

    'aspect-ratio-calculator': {
        html: `<div class="dashboard-view active">
    <h2>Aspect Ratio Calculator</h2>
    <p class="view-description">Calculate dimensions using presets or find the ratio of custom sizes.</p>

    <div class="tool-section">
        <h3>Preset Calculator</h3>
        <div class="calculator-grid">
            <div class="form-group">
                <label for="aspect-ratio-select">Aspect Ratio</label>
                <select id="aspect-ratio-select">
                    <option value="1/1">1:1 (Square)</option>
                    <option value="4/3">4:3 (Standard)</option>
                    <option value="3/2">3:2 (Photography)</option>
                    <option value="16/9" selected>16:9 (Widescreen)</option>
                    <option value="9/16">9:16 (Vertical Video)</option>
                    <option value="21/9">21:9 (Ultrawide)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="preset-width">Width</label>
                <input type="number" id="preset-width" placeholder="e.g., 1920">
            </div>
            <div class="form-group">
                <label for="preset-height">Height</label>
                <input type="number" id="preset-height" placeholder="e.g., 1080">
            </div>
        </div>
    </div>

    <div class="tool-section">
        <h3>Custom Ratio Finder</h3>
        <div class="calculator-grid">
            <div class="form-group">
                <label for="custom-width">Width</label>
                <input type="number" id="custom-width" placeholder="Enter width">
            </div>
            <div class="form-group">
                <label for="custom-height">Height</label>
                <input type="number" id="custom-height" placeholder="Enter height">
            </div>
             <div class="form-group button-group">
                <button id="calculate-custom-ratio" class="action-button">Calculate Ratio</button>
            </div>
        </div>
        <div id="custom-ratio-result" class="result-box">
            Your calculated ratio will appear here.
        </div>
    </div>
</div>`,
        css: `/* --- Aspect Ratio Calculator Styles --- */
.tool-section {
    background-color: var(--surface-color);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.tool-section h3 {
    margin-bottom: 1.5rem;
    color: #fff;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
}

.calculator-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-color);
}

.form-group.button-group {
    justify-content: flex-end;
}

input[type="number"], select {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s;
}

input[type="number"]:focus, select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.2);
}

.result-box {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: var(--bg-color);
    border-radius: 8px;
    text-align: center;
    font-size: 1.1rem;
    color: var(--subtle-text-color);
    transition: all 0.3s;
}

.result-box.success {
    color: var(--success-color);
    font-weight: 500;
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
    .calculator-grid {
        grid-template-columns: 1fr;
    }
}`,
        init: function() {
            initializeAspectRatioCalculator();

    function initializeAspectRatioCalculator() {

        const select = document.getElementById('aspect-ratio-select');
        const presetWidthInput = document.getElementById('preset-width');
        const presetHeightInput = document.getElementById('preset-height');

        const customWidthInput = document.getElementById('custom-width');
        const customHeightInput = document.getElementById('custom-height');
        const calculateBtn = document.getElementById('calculate-custom-ratio');
        const resultBox = document.getElementById('custom-ratio-result');

        select?.addEventListener('change', () => handlePresetCalculation('width'));
        presetWidthInput?.addEventListener('input', () => handlePresetCalculation('width'));
        presetHeightInput?.addEventListener('input', () => handlePresetCalculation('height'));

        calculateBtn?.addEventListener('click', calculateCustomRatio);


        function handlePresetCalculation(changedInput) {
            const ratio = select.value.split('/').map(Number); // e.g., [16, 9]
            const width = parseFloat(presetWidthInput.value);
            const height = parseFloat(presetHeightInput.value);

            if (changedInput === 'width' && width > 0) {
                const newHeight = (width / ratio[0]) * ratio[1];
                presetHeightInput.value = Math.round(newHeight);
            } else if (changedInput === 'height' && height > 0) {
                const newWidth = (height / ratio[1]) * ratio[0];
                presetWidthInput.value = Math.round(newWidth);
            }
        }

        function calculateCustomRatio() {
            const width = parseInt(customWidthInput.value);
            const height = parseInt(customHeightInput.value);

            if (!width || !height || width <= 0 || height <= 0) {
                resultBox.textContent = "Please enter valid width and height.";
                resultBox.classList.remove('success');
                return;
            }

            const divisor = gcd(width, height);
            const simplifiedWidth = width / divisor;
            const simplifiedHeight = height / divisor;
            const decimalRatio = (width / height).toFixed(3);

            resultBox.innerHTML = `Simplified: <strong>${simplifiedWidth}:${simplifiedHeight}</strong> (Decimal: ${decimalRatio})`;
            resultBox.classList.add('success');
        }

        function gcd(a, b) {
            while (b) {
                [a, b] = [b, a % b];
            }
            return a;
        }
    }
        }
    },

    'prompt-generator': {
        html: `<div class="dashboard-view active">
    <h2>Prompt Generator (For Inspire-pack)</h2>
    <p class="view-description">Automated format generator specifically for Inspire-pack. Separate prompts with a blank line to generate your formatted text file.</p>
    <div class="prompt-box-wrapper">
        <div class="prompt-box">
            <label for="positive-prompts">✅ Positive Prompts</label>
            <textarea id="positive-prompts" placeholder="A stunning portrait...&#10;&#10;A beautiful landscape..."></textarea>
        </div>
        <div class="prompt-box">
            <label for="negative-prompts">❌ Negative Prompts</label>
            <textarea id="negative-prompts" placeholder="blurry, ugly..."></textarea>
        </div>
    </div>
    <div class="options-container">
        <label class="toggle-switch">
            <input type="checkbox" id="apply-all-negative">
            <span class="slider"></span>
        </label>
        <label for="apply-all-negative">Use the first negative prompt for all</label>
    </div>
    <button id="export-button" class="action-button">Export to .txt</button>
</div>`,
        css: `/* --- Prompt Generator Styles --- */
.prompt-box-wrapper {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.prompt-box {
    flex: 1;
}

.prompt-box label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-weight: 500;
    font-size: 1rem;
}

textarea {
    width: 100%;
    height: 350px;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.95rem;
    font-family: 'Poppins', sans-serif;
    resize: vertical;
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: all 0.3s;
}

textarea::placeholder {
    color: var(--subtle-text-color);
}

textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.2);
}

.options-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 2rem 0;
    padding: 1rem;
    background-color: var(--bg-color);
    border-radius: 8px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 28px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--subtle-text-color);
    transition: .4s;
    border-radius: 28px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(22px);
}

.options-container label {
    margin-left: 12px;
    cursor: pointer;
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
    .prompt-box-wrapper {
        flex-direction: column;
    }
}`,
        init: function() {
            initializePromptGenerator();

    function initializePromptGenerator() {
        const exportButton = document.getElementById('export-button');
        if (exportButton) {
            exportButton.addEventListener('click', exportPrompts);
        }
    }

    function exportPrompts() {
        const positiveText = document.getElementById('positive-prompts').value.trim();
        const negativeText = document.getElementById('negative-prompts').value.trim();
        const applyAllNegative = document.getElementById('apply-all-negative').checked;

        if (!positiveText) {
            alert('Please enter at least one positive prompt.');
            return;
        }

        const positivePrompts = positiveText.split(/\n\s*\n/).filter(prompt => prompt.trim());
        const negativePrompts = negativeText.split(/\n\s*\n/).filter(prompt => prompt.trim());

        let output = '';
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);

        positivePrompts.forEach((positivePrompt, index) => {
            const cleanPositive = positivePrompt.replace(/\n/g, ' ').trim();

            let negativePrompt = '';
            if (applyAllNegative && negativePrompts.length > 0) {

                negativePrompt = negativePrompts[0].replace(/\n/g, ' ').trim();
            } else if (negativePrompts[index]) {

                negativePrompt = negativePrompts[index].replace(/\n/g, ' ').trim();
            }

            output += `positive:${cleanPositive}\n\n`;
            output += `negative:${negativePrompt}\n`;

            if (index < positivePrompts.length - 1) {
                output += '-----------------\n';
            }
        });

        const blob = new Blob([output], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `generate.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        const originalText = exportButton.textContent;
        exportButton.textContent = '✓ Exported!';
        exportButton.style.backgroundColor = 'var(--success-color)';

        setTimeout(() => {
            exportButton.textContent = originalText;
            exportButton.style.backgroundColor = '';
        }, 2000);
    }
        }
    },

    'settings': {
        html: `<div class="dashboard-view active">
    <h2>Settings</h2>
    <p class="view-description">Configure your dashboard preferences and tool settings.</p>

    <div class="settings-section">
        <h3>
            <svg class="section-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="5"/>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
            </svg>
            Appearance
        </h3>
        <div class="setting-group">
            <label for="theme-select" class="setting-label">Theme</label>
            <p class="setting-description">Choose your preferred color theme for the dashboard</p>
            <select id="theme-select" class="setting-select">
                <option value="dark">Dark (Default)</option>
                <option value="light">Light</option>
                <option value="ocean">Ocean</option>
                <option value="forest">Forest</option>
                <option value="purple">Purple</option>
            </select>
        </div>
    </div>

    <div class="settings-section">
        <h3>
            <svg class="section-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            Preferences
        </h3>
        <div class="setting-group">
            <label class="setting-checkbox">
                <input type="checkbox" id="auto-save-settings">
                <span class="checkmark"></span>
                <div class="checkbox-content">
                    <span class="checkbox-label">Auto-save settings</span>
                    <span class="checkbox-description">Automatically save your preferences</span>
                </div>
            </label>
        </div>
        <div class="setting-group">
            <label class="setting-checkbox">
                <input type="checkbox" id="show-tooltips" checked>
                <span class="checkmark"></span>
                <div class="checkbox-content">
                    <span class="checkbox-label">Show tooltips</span>
                    <span class="checkbox-description">Display helpful tooltips throughout the interface</span>
                </div>
            </label>
        </div>
    </div>

    <div class="settings-section">
        <h3>
            <svg class="section-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 12l2 2 4-4"/>
                <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
                <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
                <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3"/>
                <path d="M12 21c0-1 1-3 3-3s3 2 3 3-1 3-3 3-3-2-3-3"/>
            </svg>
            Actions
        </h3>
        <div class="setting-group">
            <button id="reset-settings" class="action-button secondary">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M1 4v6h6"/>
                    <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                </svg>
                Reset to Defaults
            </button>
            <button id="clear-cache" class="action-button danger" title="Clear local storage, caches, and IndexedDB for this app">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="3 6 5 6 21 6"/>
                    <path d="M19 6l-1 14a2 2 0 0 1-2 2H8a2 2 0 0 1-2-2L5 6"/>
                    <path d="M10 11v6"/>
                    <path d="M14 11v6"/>
                    <path d="M9 6V4a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v2"/>
                </svg>
                Clear Cache
            </button>
        </div>
    </div>

    <div class="settings-section">
        <h3>
            <svg class="section-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2l3 7h7l-5.5 4 2 7-6.5-4.5L5.5 20l2-7L2 9h7l3-7z"/>
            </svg>
            AI Models
        </h3>
        <div class="setting-group">
            <label for="llm-provider" class="setting-label">Provider</label>
            <select id="llm-provider" class="setting-select">
                <option value="openai">OpenAI</option>
                <option value="gemini">Gemini</option>
                <option value="groq">Groq</option>
                <option value="ollama">Ollama (local)</option>
                <option value="lmstudio">LM Studio (local)</option>
            </select>
        </div>
        <div class="setting-group">
            <label for="llm-api-key" class="setting-label">API Key (if required)</label>
            <input type="password" id="llm-api-key" placeholder="Enter API key for selected provider" />
            <div class="settings-warning" role="note" aria-live="polite">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"/>
                    <path d="M12 16v-4"/>
                    <path d="M12 8h.01"/>
                </svg>
                <span>Your API keys are stored locally in your browser on this device. Do not share passwords or export settings if others can access them.</span>
            </div>
        </div>
        <div class="setting-group">
            <label for="llm-base-url" class="setting-label">Base URL (optional)</label>
            <input type="text" id="llm-base-url" placeholder="e.g., http://localhost:1234 for local providers" />
        </div>
        <div class="grid-2">
            <div class="setting-group">
                <label for="llm-model" class="setting-label">Default Text Model</label>
                <input type="text" id="llm-model" placeholder="e.g., gpt-4o-mini, gemini-1.5-flash, llama3.1" />
            </div>
            <div class="setting-group">
                <label for="llm-vision-model" class="setting-label">Vision Model</label>
                <input type="text" id="llm-vision-model" placeholder="e.g., gpt-4o, gemini-1.5-pro, (optional)" />
            </div>
        </div>
        <p class="settings-info">These settings are used by AI Image Prompt tool.</p>
    </div>

    <div class="settings-footer">
        <p class="settings-info">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"/>
                <path d="M12 16v-4"/>
                <path d="M12 8h.01"/>
            </svg>
            Settings are automatically saved to your browser's local storage
        </p>
    </div>
</div>`,
        css: `/* Settings Tool Styles */
.settings-section {
    background-color: var(--surface-color);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.settings-section h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    color: #fff;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
    font-size: 1.1rem;
}

.section-icon {
    color: var(--primary-color);
}

.setting-group {
    margin-bottom: 1.5rem;
}

/* Inputs - match theme */
.settings-section input[type="text"],
.settings-section input[type="password"],
.settings-section input[type="url"],
.settings-section input[type="email"],
.settings-section textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s;
}

.settings-section input::placeholder,
.settings-section textarea::placeholder {
    color: var(--subtle-text-color);
}

.settings-section input:focus,
.settings-section textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.2);
}

/* Grid utility for pairs */
.grid-2 {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1rem 1.5rem;
    align-items: end;
}

/* Warning note */
.settings-warning {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--warning-color);
    background: rgba(224, 175, 104, 0.08);
    border: 1px solid rgba(224, 175, 104, 0.3);
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    margin-top: 0.5rem;
}

.settings-warning svg {
    flex-shrink: 0;
}

/* Danger variant */
.action-button.danger {
    background-color: rgba(247, 118, 142, 0.15);
    color: var(--error-color);
    border: 1px solid rgba(247, 118, 142, 0.35);
    width: auto;
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
}

.action-button.danger:hover {
    background-color: rgba(247, 118, 142, 0.25);
}


.setting-group:last-child {
    margin-bottom: 0;
}

.setting-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.setting-description {
    font-size: 0.9rem;
    color: var(--subtle-text-color);
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.setting-select {
    width: 100%;
    max-width: 300px;
    padding: 0.75rem 1rem;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s;
    cursor: pointer;
}

.setting-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.2);
}

/* Custom Checkbox Styles */
.setting-checkbox {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 8px;
    transition: background-color 0.3s;
}

.setting-checkbox:hover {
    background-color: rgba(122, 162, 247, 0.05);
}

.setting-checkbox input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s;
    flex-shrink: 0;
    margin-top: 2px;
}

.setting-checkbox input[type="checkbox"]:checked + .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.setting-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-content {
    flex: 1;
}

.checkbox-label {
    display: block;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.checkbox-description {
    font-size: 0.9rem;
    color: var(--subtle-text-color);
    line-height: 1.4;
}

/* Secondary Action Button */
.action-button.secondary {
    background-color: var(--surface-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    width: auto;
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
}

.action-button.secondary:hover {
    background-color: var(--border-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Settings Footer */
.settings-footer {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.settings-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--subtle-text-color);
    margin: 0;
}

.settings-info svg {
    color: var(--primary-color);
    flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .settings-section {
        padding: 1.5rem;
    }

    .setting-checkbox {
        padding: 0.5rem;
    }
}`,
        init: function() {
            initializeSettings();

    function initializeSettings() {

        loadSettings();

        setupEventListeners();

        console.log('Settings tool loaded');
    }

    function setupEventListeners() {

        const themeSelect = document.getElementById('theme-select');
        if (themeSelect) {
            themeSelect.addEventListener('change', handleThemeChange);
        }

        const autoSaveCheckbox = document.getElementById('auto-save-settings');
        if (autoSaveCheckbox) {
            autoSaveCheckbox.addEventListener('change', handleAutoSaveChange);
        }

        const tooltipsCheckbox = document.getElementById('show-tooltips');
        if (tooltipsCheckbox) {
            tooltipsCheckbox.addEventListener('change', handleTooltipsChange);
        }

        const resetButton = document.getElementById('reset-settings');
        if (resetButton) {
            resetButton.addEventListener('click', handleResetSettings);
        }

        const clearCacheBtn = document.getElementById('clear-cache');
        if (clearCacheBtn) {
            clearCacheBtn.addEventListener('click', handleClearCache);
        }


        const provider = document.getElementById('llm-provider');
        const apiKey = document.getElementById('llm-api-key');
        const baseUrl = document.getElementById('llm-base-url');
        const model = document.getElementById('llm-model');
        const visionModel = document.getElementById('llm-vision-model');
        [provider, apiKey, baseUrl, model, visionModel].forEach(el => {
            if (el) el.addEventListener('change', handleLLMChange);
            if (el) el.addEventListener('input', handleLLMChange);
        });
    }

    function handleThemeChange(event) {
        const selectedTheme = event.target.value;
        applyTheme(selectedTheme);
        saveSettings();

        showSettingsFeedback('Theme changed successfully!');
    }

    function applyTheme(theme) {

        if (theme === 'dark') {
            document.documentElement.removeAttribute('data-theme');
        } else {
            document.documentElement.setAttribute('data-theme', theme);
        }
    }

    function handleAutoSaveChange(event) {
        const isEnabled = event.target.checked;
        saveSettings();
        showSettingsFeedback(`Auto-save ${isEnabled ? 'enabled' : 'disabled'}`);
    }

    function handleTooltipsChange(event) {
        const isEnabled = event.target.checked;
        saveSettings();
        showSettingsFeedback(`Tooltips ${isEnabled ? 'enabled' : 'disabled'}`);
    }

    function handleResetSettings() {
        if (confirm('Are you sure you want to reset all settings to their defaults? This action cannot be undone.')) {

            const defaults = {
                theme: 'dark',
                autoSave: false,
                showTooltips: true,
                llm: { provider: 'openai', apiKey: '', baseUrl: '', model: '', visionModel: '' }
            };

            document.getElementById('theme-select').value = defaults.theme;
            document.getElementById('auto-save-settings').checked = defaults.autoSave;
            document.getElementById('show-tooltips').checked = defaults.showTooltips;
            if (document.getElementById('llm-provider')) document.getElementById('llm-provider').value = defaults.llm.provider;
            if (document.getElementById('llm-api-key')) document.getElementById('llm-api-key').value = defaults.llm.apiKey;
            if (document.getElementById('llm-base-url')) document.getElementById('llm-base-url').value = defaults.llm.baseUrl;
            if (document.getElementById('llm-model')) document.getElementById('llm-model').value = defaults.llm.model;
            if (document.getElementById('llm-vision-model')) document.getElementById('llm-vision-model').value = defaults.llm.visionModel;

            applyTheme(defaults.theme);

            localStorage.setItem('comfyui-helper-settings', JSON.stringify(defaults));

            showSettingsFeedback('Settings reset to defaults');
        }
    }

    function saveSettings() {
        const llm = {
            provider: document.getElementById('llm-provider')?.value || 'openai',
            apiKey: document.getElementById('llm-api-key')?.value || '',
            baseUrl: document.getElementById('llm-base-url')?.value || '',
            model: document.getElementById('llm-model')?.value || '',
            visionModel: document.getElementById('llm-vision-model')?.value || ''
        };
        const settings = {
            theme: document.getElementById('theme-select')?.value || 'dark',
            autoSave: document.getElementById('auto-save-settings')?.checked || false,
            showTooltips: document.getElementById('show-tooltips')?.checked || true,


            llm
        };

        localStorage.setItem('comfyui-helper-settings', JSON.stringify(settings));
    }

    function loadSettings() {
        try {
            const savedSettings = localStorage.getItem('comfyui-helper-settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);

                if (settings.theme) {
                    const themeSelect = document.getElementById('theme-select');
                    if (themeSelect) {
                        themeSelect.value = settings.theme;
                        applyTheme(settings.theme);
                    }
                }

                const autoSaveCheckbox = document.getElementById('auto-save-settings');
                if (autoSaveCheckbox && typeof settings.autoSave === 'boolean') {
                    autoSaveCheckbox.checked = settings.autoSave;
                }

                const tooltipsCheckbox = document.getElementById('show-tooltips');
                if (tooltipsCheckbox && typeof settings.showTooltips === 'boolean') {
                    tooltipsCheckbox.checked = settings.showTooltips;
                }

                if (settings.llm) {
                    const p = document.getElementById('llm-provider');
                    const k = document.getElementById('llm-api-key');
                    const u = document.getElementById('llm-base-url');
                    const m = document.getElementById('llm-model');
                    const vm = document.getElementById('llm-vision-model');
                    if (p && settings.llm.provider) p.value = settings.llm.provider;
                    if (k && typeof settings.llm.apiKey === 'string') k.value = settings.llm.apiKey;
                    if (u && typeof settings.llm.baseUrl === 'string') u.value = settings.llm.baseUrl;
                    if (m && typeof settings.llm.model === 'string') m.value = settings.llm.model;
                    if (vm && typeof settings.llm.visionModel === 'string') vm.value = settings.llm.visionModel;
                }
            }
        } catch (error) {
            console.warn('Failed to load settings:', error);
        }
    }

    // Clear all caches/storage for this app
    async function handleClearCache() {
        if (!confirm('This will remove all locally stored data for this app (settings, caches, and databases). Continue?')) return;
        try {
            localStorage.clear();
            if (window.sessionStorage) sessionStorage.clear();
            if (window.caches && caches.keys) {
                const names = await caches.keys();
                await Promise.all(names.map(n => caches.delete(n)));
            }
            if (window.indexedDB && indexedDB.databases) {
                const dbs = await indexedDB.databases();
                await Promise.all((dbs || []).map(db => db?.name ? new Promise((resolve) => {
                    const req = indexedDB.deleteDatabase(db.name);
                    req.onsuccess = req.onerror = req.onblocked = () => resolve();
                }) : Promise.resolve()));
            }
            showSettingsFeedback('Local cache cleared');
        } catch (e) {
            console.warn('Failed clearing cache:', e);
            showSettingsFeedback('Some cache items could not be cleared');
        }
        setTimeout(() => location.reload(), 400);
    }

    function handleLLMChange(){
        saveSettings();
        showSettingsFeedback('AI model settings saved');
    }

    function showSettingsFeedback(message) {

        let feedback = document.querySelector('.settings-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'settings-feedback';
            feedback.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: var(--success-color);
                color: white;
                padding: 0.75rem 1rem;
                border-radius: 8px;
                font-size: 0.9rem;
                font-weight: 500;
                z-index: 1000;
                opacity: 0;
                transform: translateY(-10px);
                transition: all 0.3s ease;
            `;
            document.body.appendChild(feedback);
        }

        feedback.textContent = message;

        requestAnimationFrame(() => {
            feedback.style.opacity = '1';
            feedback.style.transform = 'translateY(0)';
        });

        setTimeout(() => {
            feedback.style.opacity = '0';
            feedback.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                if (feedback.parentNode) {
                    feedback.parentNode.removeChild(feedback);
                }
            }, 300);
        }, 3000);
    }

    function initializeThemeOnLoad() {
        try {
            const savedSettings = localStorage.getItem('comfyui-helper-settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                if (settings.theme) {
                    applyTheme(settings.theme);
                }
            }
        } catch (error) {
            console.warn('Failed to initialize theme:', error);
        }
    }

    initializeThemeOnLoad();
        }
    },

};


// --- TOOL LOADING SYSTEM (No Server Required) ---
async function loadTool(toolName) {
    const contentArea = document.getElementById('tool-content');

    try {
        // Show loading state
        contentArea.innerHTML = '<div class="loading">Loading...</div>';

        // Get tool template
        const toolTemplate = TOOL_TEMPLATES[toolName];
        if (!toolTemplate) {
            throw new Error(`Tool ${toolName} not found`);
        }

        // Load HTML content
        contentArea.innerHTML = toolTemplate.html;

        // Load CSS
        loadToolCSS(toolName, toolTemplate.css);

        // Initialize tool functionality
        if (toolTemplate.init) {
            toolTemplate.init();
        }

    } catch (error) {
        console.error(`Error loading tool ${toolName}:`, error);
        contentArea.innerHTML = `<div class="error">Failed to load ${toolName}. Please try again.</div>`;
    }
}

// Load tool-specific CSS
function loadToolCSS(toolName, cssContent) {
    const cssId = `${toolName}-css`;

    // Remove existing tool CSS
    const existingCSS = document.getElementById(cssId);
    if (existingCSS) {
        existingCSS.remove();
    }

    // Add new CSS
    const style = document.createElement('style');
    style.id = cssId;
    style.textContent = cssContent;
    document.head.appendChild(style);
}

// --- THEME INITIALIZATION ---
function initializeTheme() {
    try {
        const savedSettings = localStorage.getItem('comfyui-helper-settings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);
            if (settings.theme && settings.theme !== 'dark') {
                document.documentElement.setAttribute('data-theme', settings.theme);
            }
        }
    } catch (error) {
        console.warn('Failed to initialize theme:', error);
    }
}
