/* Settings Tool Styles */
.settings-section {
    background-color: var(--surface-color);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.settings-section h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    color: #fff;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
    font-size: 1.1rem;
}

.section-icon {
    color: var(--primary-color);
}

.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group:last-child {
    margin-bottom: 0;
}

.setting-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.setting-description {
    font-size: 0.9rem;
    color: var(--subtle-text-color);
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

/* Inputs - match theme */
.settings-section input[type="text"],
.settings-section input[type="password"],
.settings-section input[type="url"],
.settings-section input[type="email"],
.settings-section textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s;
}

.settings-section input::placeholder,
.settings-section textarea::placeholder {
    color: var(--subtle-text-color);
}

.settings-section input:focus,
.settings-section textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.2);
}

.setting-select {
    width: 100%;
    max-width: 300px;
    padding: 0.75rem 1rem;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s;
    cursor: pointer;
}

.setting-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.2);
}

/* Grid utility for pairs */
.grid-2 {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1rem 1.5rem;
    align-items: end;
}

/* Warning note */
.settings-warning {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--warning-color);
    background: rgba(224, 175, 104, 0.08);
    border: 1px solid rgba(224, 175, 104, 0.3);
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    margin-top: 0.5rem;
}

.settings-warning svg {
    flex-shrink: 0;
}

/* Custom Checkbox Styles */
.setting-checkbox {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 8px;
    transition: background-color 0.3s;
}

.setting-checkbox:hover {
    background-color: rgba(122, 162, 247, 0.05);
}

.setting-checkbox input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s;
    flex-shrink: 0;
    margin-top: 2px;
}

.setting-checkbox input[type="checkbox"]:checked + .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.setting-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-content {
    flex: 1;
}

.checkbox-label {
    display: block;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.checkbox-description {
    font-size: 0.9rem;
    color: var(--subtle-text-color);
    line-height: 1.4;
}

/* Secondary Action Button */
.action-button.secondary {
    background-color: var(--surface-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    width: auto;
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
}

.action-button.secondary:hover {
    background-color: var(--border-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Danger variant */
.action-button.danger {
    background-color: rgba(247, 118, 142, 0.15);
    color: var(--error-color);
    border: 1px solid rgba(247, 118, 142, 0.35);
    width: auto;
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
}

.action-button.danger:hover {
    background-color: rgba(247, 118, 142, 0.25);
}

/* Settings Footer */
.settings-footer {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.settings-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--subtle-text-color);
    margin: 0;
}

.settings-info svg {
    color: var(--primary-color);
    flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .settings-section {
        padding: 1.5rem;
    }

    .setting-checkbox {
        padding: 0.5rem;
    }
}