// --- AI IMAGE PROMPT TOOL ---
(function() {
    initAIImagePromptTool();

    function initAIImagePromptTool() {
        // Tabs
        document.querySelectorAll('.tab-button').forEach(btn => btn.addEventListener('click', onTabClick));

        // File previews
        setupPreview('img2prompt-file', 'img2prompt-preview');
        setupPreview('describe-file', 'describe-preview');

        // Actions
        onClick('img2prompt-generate', handleImg2Prompt);
        onClick('img2prompt-copy', () => copyFrom('img2prompt-output'));
        onClick('magic-generate', handleMagicEnhance);
        onClick('magic-copy', () => copyFrom('magic-output'));
        onClick('describe-generate', handleDescribe);
        onClick('describe-copy', () => copyFrom('describe-output'));

        // Describe: toggle custom question field
        const optionSel = document.getElementById('describe-option');
        if (optionSel) optionSel.addEventListener('change', () => {
            const isCustom = optionSel.value === 'custom';
            document.getElementById('custom-question-wrap')?.classList.toggle('hidden', !isCustom);
        });

        // Word count slider wiring
        const wordSlider = document.getElementById('word-count');
        const wordVal = document.getElementById('word-count-value');
        if (wordSlider && wordVal) {
            const update = () => {
                const v = parseInt(wordSlider.value, 10) || 100;
                wordVal.textContent = String(v);
                wordSlider.setAttribute('aria-valuenow', String(v));
            };
            update();
            wordSlider.addEventListener('input', update);
        }
    }

    function onTabClick(e) {
        const target = e.currentTarget;
        const tab = target.getAttribute('data-tab');
        document.querySelectorAll('.tab-button').forEach(b => b.classList.remove('active'));
        target.classList.add('active');
        document.querySelectorAll('.tab-panel').forEach(p => p.classList.add('hidden'));
        document.getElementById(`tab-${tab}`)?.classList.remove('hidden');
    }

    function onClick(id, fn) {
        const el = document.getElementById(id);
        if (el) el.addEventListener('click', fn);
    }

    function setupPreview(inputId, imgId) {
        const input = document.getElementById(inputId);
        const img = document.getElementById(imgId);
        if (!input || !img) return;
        input.addEventListener('change', () => {
            const file = input.files && input.files[0];
            if (!file) return;
            const reader = new FileReader();
            reader.onload = () => { img.src = reader.result; };
            reader.readAsDataURL(file);
        });
    }

    function getSettings() {
        try {
            const raw = localStorage.getItem('comfyui-helper-settings');
            return raw ? JSON.parse(raw) : {};
        } catch { return {}; }
    }

    function getLLMConfig() {
        const s = getSettings();
        return s.llm || { provider: 'openai', apiKey: '', baseUrl: '' };
    }

    function setOutput(id, text) {
        const el = document.getElementById(id);
        if (el) el.value = text || '';
    }

    // Global target word helpers
    function getTargetWords(){
        const el = document.getElementById('word-count');
        const v = parseInt(el && el.value, 10);
        if (!Number.isFinite(v)) return 100;
        return Math.min(400, Math.max(10, v));
    }

    function lengthInstruction(){
        const n = getTargetWords();
        return `Aim for no more than ${n} words. Shorter is fine if it improves quality.`;
    }


    async function handleImg2Prompt() {
        const modelStyle = document.getElementById('img2prompt-model')?.value || 'general';
        const fileInput = document.getElementById('img2prompt-file');
        const file = fileInput && fileInput.files && fileInput.files[0];
        if (!file) { alert('Please upload an image.'); return; }
        setImg2PromptLoading(true);
        try {
            const prompt = await callVisionPrompt(file, buildImg2PromptSystem(modelStyle), 'Describe this image for the target format.');
            setOutput('img2prompt-output', formatPromptByStyle(prompt, modelStyle));
        } catch (e) {
            setOutput('img2prompt-output', `Error: ${e.message || e}`);
        } finally {
            setImg2PromptLoading(false);
        }
    }

    async function handleMagicEnhance() {
        const idea = document.getElementById('magic-input')?.value?.trim();
        if (!idea) { alert('Please enter some text.'); return; }
        const modelStyle = document.getElementById('magic-model')?.value || 'general';
        setMagicLoading(true);
        try {
            const out = await callTextPrompt(buildMagicEnhanceSystem(modelStyle), `Idea: ${idea}`);
            // Only output the expanded prompt (strip any echoes like "Idea:" etc.)
            const cleaned = cleanExpandedOutput(out);
            setOutput('magic-output', formatPromptByStyle(cleaned, modelStyle));
        } catch (e) {
            setOutput('magic-output', `Error: ${e.message || e}`);
        } finally {
            setMagicLoading(false);
        }
    }

    async function handleDescribe() {
        const fileInput = document.getElementById('describe-file');
        const file = fileInput && fileInput.files && fileInput.files[0];
        if (!file) { alert('Please upload an image.'); return; }
        const opt = document.getElementById('describe-option')?.value || 'detail';
        let user = '';
        switch(opt){
            case 'detail': user = 'Provide a thorough, multi-paragraph description of the image.'; break;
            case 'brief': user = 'Provide a concise 2-3 sentence description of the image.'; break;
            case 'person': user = 'Describe the person in the image, including clothing, pose, expression.'; break;
            case 'objects': user = 'List and briefly describe the main objects in the image.'; break;
            case 'art': user = 'Analyze the art style, medium, influences, composition, and color palette.'; break;
            case 'ocr': user = 'Extract all visible text (OCR) from the image.'; break;
            case 'general': user = 'Write a general image prompt suitable for text-to-image.'; break;
            case 'flux': user = 'Write a Flux-style prompt optimized for Flux models.'; break;
            case 'sd': user = 'Write a Stable Diffusion style prompt.'; break;
            case 'custom': user = document.getElementById('custom-question')?.value?.trim() || 'Describe the image.'; break;
        }
        setDescribeLoading(true);
        try {
            const system = buildDescribeSystem(opt);
            const out = await callVisionPrompt(file, system, user);
            const style = opt === 'flux' ? 'flux' : opt === 'sd' ? 'sd' : 'general';
            setOutput('describe-output', formatPromptByStyle(out, style));
        } catch (e) {
            setOutput('describe-output', `Error: ${e.message || e}`);
        } finally {
            setDescribeLoading(false);
        }
    }

    // --- Prompt style builders ---
    function buildImg2PromptSystem(style){
        let base;
        switch(style){
            case 'flux':
                base = `You are an AI assistant specialized in creating natural-language image generation prompts in the Flux style. Always interpret prompts as flowing, cinematic descriptions rather than keyword lists. Read the prompt like a vivid scene in a novel, and generate imagery that captures mood, lighting, and emotion alongside physical details. Use context to infer composition and atmosphere. Focus on photorealism or stylized realism depending on the wording, but never reduce the prompt to disconnected keywords.

Your priorities:
1. Treat prompts as human storytelling — scene first, then mood, then visual elements.
2. Capture light, texture, depth, and atmosphere.
3. Maintain accurate perspective and proportions.
4. Preserve artistic cohesion — the scene should feel alive, not artificially assembled.

Convert images into concise, high-signal Flux-style prompts (no extra commentary).`;
                break;
            case 'sd':
                base = `You are an AI assistant specialized in creating image generation prompts in the SDXL/Stable Diffusion style. Interpret prompts as a prioritized checklist of visual elements, separated by commas. Respect weighting syntax (element:1.3) to emphasize certain parts. Avoid reordering unless needed for composition.

Your priorities:
1. Parse prompts into distinct chunks (subject, setting, style, mood, resolution).
2. Apply emphasis weights accurately to adjust importance.
3. Maintain sharpness and clarity in high-detail requests.
4. When unsure, default to a balanced composition rather than random placement.
5. Honor explicit descriptors like photorealistic, ultra-detailed, or cinematic lighting exactly as stated.

Convert images into Stable Diffusion prompts with positive prompt only, no negative unless asked.`;
                break;
            default:
                base = `You are an AI assistant specialized in creating natural-language image generation prompts with strong contextual understanding. Read prompts like a detailed layered description. Interpret them in four stages: (1) the core subject, (2) the environment or setting, (3) the mood and atmosphere, and (4) fine details like lighting, textures, and accessories.

Your priorities:
1. Begin by identifying the subject and their role in the scene.
2. Layer in the background and setting with rich, immersive detail.
3. Apply mood elements (lighting, color grading, emotional tone).
4. Render realistic textures and refined details.
5. Maintain consistency in style and avoid unrelated visual clutter.

You describe images as natural language prompts for text-to-image generation.`;
                break;
        }
        return base + ' ' + lengthInstruction();
    }

    function buildMagicEnhanceSystem(style){
        const common = 'You will be given a short idea. Produce ONE single best prompt ONLY. Do not include any explanations, headings, lists, markdown, or quotes. Output only the final prompt text.';
        switch(style){
            case 'flux':
                return `You are an AI assistant specialized in creating natural-language image generation prompts in the Flux style. Always interpret prompts as flowing, cinematic descriptions rather than keyword lists. Read the prompt like a vivid scene in a novel, and generate imagery that captures mood, lighting, and emotion alongside physical details. Use context to infer composition and atmosphere. Focus on photorealism or stylized realism depending on the wording, but never reduce the prompt to disconnected keywords.

Your priorities:
1. Treat prompts as human storytelling — scene first, then mood, then visual elements.
2. Capture light, texture, depth, and atmosphere.
3. Maintain accurate perspective and proportions.
4. Preserve artistic cohesion — the scene should feel alive, not artificially assembled.

${common} Format as a concise Flux-style prompt focusing on subject, style, lighting, composition, camera. Keep one sentence if possible. ` + lengthInstruction();
            case 'sd':
                return `You are an AI assistant specialized in creating image generation prompts in the SDXL/Stable Diffusion style. Interpret prompts as a prioritized checklist of visual elements, separated by commas. Respect weighting syntax (element:1.3) to emphasize certain parts. Avoid reordering unless needed for composition.

Your priorities:
1. Parse prompts into distinct chunks (subject, setting, style, mood, resolution).
2. Apply emphasis weights accurately to adjust importance.
3. Maintain sharpness and clarity in high-detail requests.
4. When unsure, default to a balanced composition rather than random placement.
5. Honor explicit descriptors like photorealistic, ultra-detailed, or cinematic lighting exactly as stated.

${common} Format as a Stable Diffusion positive prompt: comma-separated attributes; no negatives unless specified. ` + lengthInstruction();
            default:
                return `You are an AI assistant specialized in creating natural-language image generation prompts with strong contextual understanding. Read prompts like a detailed layered description. Interpret them in four stages: (1) the core subject, (2) the environment or setting, (3) the mood and atmosphere, and (4) fine details like lighting, textures, and accessories.

Your priorities:
1. Begin by identifying the subject and their role in the scene.
2. Layer in the background and setting with rich, immersive detail.
3. Apply mood elements (lighting, color grading, emotional tone).
4. Render realistic textures and refined details.
5. Maintain consistency in style and avoid unrelated visual clutter.

${common} Expand into a detailed, vivid natural language prompt suitable for text-to-image. ` + lengthInstruction();
        }
    }

    function buildDescribeSystem(opt){
        if (opt === 'ocr') return 'Extract text from the image exactly, preserving line breaks if visible.';
        if (opt === 'art') return 'Analyze art style, medium, influences, composition, and palette with specifics. ' + lengthInstruction();
        if (opt === 'flux') {
            return `You are an AI assistant specialized in creating natural-language image generation prompts in the Flux style. Always interpret prompts as flowing, cinematic descriptions rather than keyword lists. Read the prompt like a vivid scene in a novel, and generate imagery that captures mood, lighting, and emotion alongside physical details. Use context to infer composition and atmosphere. Focus on photorealism or stylized realism depending on the wording, but never reduce the prompt to disconnected keywords.

Your priorities:
1. Treat prompts as human storytelling — scene first, then mood, then visual elements.
2. Capture light, texture, depth, and atmosphere.
3. Maintain accurate perspective and proportions.
4. Preserve artistic cohesion — the scene should feel alive, not artificially assembled.

Be precise and helpful about the image. ` + lengthInstruction();
        }
        if (opt === 'sd') {
            return `You are an AI assistant specialized in creating image generation prompts in the SDXL/Stable Diffusion style. Interpret prompts as a prioritized checklist of visual elements, separated by commas. Respect weighting syntax (element:1.3) to emphasize certain parts. Avoid reordering unless needed for composition.

Your priorities:
1. Parse prompts into distinct chunks (subject, setting, style, mood, resolution).
2. Apply emphasis weights accurately to adjust importance.
3. Maintain sharpness and clarity in high-detail requests.
4. When unsure, default to a balanced composition rather than random placement.
5. Honor explicit descriptors like photorealistic, ultra-detailed, or cinematic lighting exactly as stated.

Be precise and helpful about the image. ` + lengthInstruction();
        }
        return `You are an AI assistant specialized in creating natural-language image generation prompts with strong contextual understanding. Read prompts like a detailed layered description. Interpret them in four stages: (1) the core subject, (2) the environment or setting, (3) the mood and atmosphere, and (4) fine details like lighting, textures, and accessories.

Your priorities:
1. Begin by identifying the subject and their role in the scene.
2. Layer in the background and setting with rich, immersive detail.
3. Apply mood elements (lighting, color grading, emotional tone).
4. Render realistic textures and refined details.
5. Maintain consistency in style and avoid unrelated visual clutter.

Be precise and helpful about the image. ` + lengthInstruction();
    }

    function formatPromptByStyle(text, style){
        if (!text) return '';
        if (style === 'flux') {
            return text
                .replace(/\n+/g, ' ')
                .replace(/\s{2,}/g, ' ')
                .trim();
        }
        if (style === 'sd') {
            // Heuristic: ensure comma-separated attributes
            return text
                .replace(/\n+/g, ', ')
                .replace(/\s{2,}/g, ' ')
                .replace(/\,\s*\,/g, ', ')
                .trim();
        }
        return text.trim();
    }

    function cleanExpandedOutput(text){
        if (!text) return '';
        // Normalize model output; strip labels and bullets
        let cleaned = text
            .replace(/^\s*(?:Idea|Original\s*Idea|Expanded\s*Prompt|Output)\s*:\s*/gim, '')
            .replace(/^\s*[\-*•]\s*/gm, '') // bullets
            .replace(/^#+\s*/gm, '')         // markdown headings
            .replace(/---+/g, ' ')            // separators
            .trim();
        // If multiple "Prompt:" labels exist, take first
        const promptMatch = cleaned.match(/Prompt\s*:\s*([\s\S]*)/i);
        if (promptMatch) cleaned = promptMatch[1].trim();
        // If it still has multiple sections, take up to first double newline
        cleaned = cleaned.split(/\n\s*\n/)[0].trim();
        return cleaned;
    }

    // --- LLM calling helpers ---
    async function callTextPrompt(system, user){
        const cfg = getLLMConfig();
        ensureProviderConfigured(cfg);
        if (cfg.provider === 'openai') {
            return callOpenAIChat(system, user, cfg);
        } else if (cfg.provider === 'gemini') {
            return callGeminiText(system, user, cfg);
        } else if (cfg.provider === 'groq') {
            return callGroqChat(system, user, cfg);
        } else if (cfg.provider === 'ollama' || cfg.provider === 'lmstudio') {
            return callLocalChat(system, user, cfg);
        } else {
            throw new Error('Unsupported provider');
        }
    }

    async function callVisionPrompt(file, system, user){
        const cfg = getLLMConfig();
        ensureProviderConfigured(cfg);
        if (cfg.provider === 'openai') {
            return callOpenAIVision(system, user, file, cfg);
        } else if (cfg.provider === 'gemini') {
            return callGeminiVision(system, user, file, cfg);
        } else if (cfg.provider === 'groq') {
            return callGroqVision(system, user, file, cfg);
        } else if (cfg.provider === 'ollama' || cfg.provider === 'lmstudio') {
            return callLocalVision(system, user, file, cfg);
        } else {
            throw new Error('Unsupported provider');
        }
    }

    function ensureProviderConfigured(cfg){
        if (!cfg || !cfg.provider) throw new Error('No provider selected in Settings');
        if (cfg.provider === 'ollama' || cfg.provider === 'lmstudio') {
            if (!cfg.baseUrl) throw new Error('Set base URL for local LLM in Settings');
        } else {
            if (!cfg.apiKey) throw new Error('Set API key for selected provider in Settings');
        }
    }

    // --- Provider implementations (lightweight; using fetch) ---
    async function callOpenAIChat(system, user, cfg){
        const url = (cfg.baseUrl || 'https://api.openai.com') + '/v1/chat/completions';
        const body = { model: cfg.model || 'gpt-4o-mini', messages: [ {role:'system', content: system}, {role:'user', content: user} ] };
        const res = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${cfg.apiKey}` }, body: JSON.stringify(body) });
        const j = await res.json();
        if (!res.ok) throw new Error(j.error?.message || 'OpenAI error');
        return j.choices?.[0]?.message?.content || '';
    }

    async function callOpenAIVision(system, user, file, cfg){
        const b64 = await fileToBase64(file);
        const url = (cfg.baseUrl || 'https://api.openai.com') + '/v1/chat/completions';
        const messages = [
            { role: 'system', content: system },
            { role: 'user', content: [ { type: 'text', text: user }, { type: 'image_url', image_url: { url: b64 } } ] }
        ];
        const body = { model: cfg.visionModel || cfg.model || 'gpt-4o-mini', messages };
        const res = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${cfg.apiKey}` }, body: JSON.stringify(body) });
        const j = await res.json();
        if (!res.ok) throw new Error(j.error?.message || 'OpenAI error');
        return j.choices?.[0]?.message?.content || '';
    }

    async function callGeminiText(system, user, cfg){
        const url = (cfg.baseUrl || 'https://generativelanguage.googleapis.com') + `/v1beta/models/${cfg.model || 'gemini-1.5-flash'}:generateContent?key=${cfg.apiKey}`;
        const body = { contents: [ { role: 'user', parts: [ { text: system + '\n' + user } ] } ] };
        const res = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(body) });
        const j = await res.json();
        if (!res.ok) throw new Error(j.error?.message || 'Gemini error');
        return j.candidates?.[0]?.content?.parts?.map(p=>p.text).join('') || '';
    }

    async function callGeminiVision(system, user, file, cfg){
        const b64 = await fileToBase64(file, true);
        const url = (cfg.baseUrl || 'https://generativelanguage.googleapis.com') + `/v1beta/models/${cfg.visionModel || cfg.model || 'gemini-1.5-flash'}:generateContent?key=${cfg.apiKey}`;
        const body = { contents: [ { role: 'user', parts: [ { text: system + '\n' + user }, { inline_data: { mime_type: file.type || 'image/png', data: b64.split(',')[1] } } ] } ] };
        const res = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(body) });
        const j = await res.json();
        if (!res.ok) throw new Error(j.error?.message || 'Gemini error');
        return j.candidates?.[0]?.content?.parts?.map(p=>p.text).join('') || '';
    }

    async function callGroqChat(system, user, cfg){
        const url = (cfg.baseUrl || 'https://api.groq.com') + '/openai/v1/chat/completions';
        const body = { model: cfg.model || 'llama-3.1-70b-versatile', messages: [ {role:'system', content: system}, {role:'user', content: user} ] };
        const res = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${cfg.apiKey}` }, body: JSON.stringify(body) });
        const j = await res.json();
        if (!res.ok) throw new Error(j.error?.message || 'Groq error');
        return j.choices?.[0]?.message?.content || '';
    }

    async function callGroqVision(system, user, file, cfg){
        // Groq vision via OpenAI-compatible endpoint may not support images universally.
        // We'll send as text fallback: describe based on user note indicating possible limitations.
        return callGroqChat(system + ' If image is unsupported, answer based on general assumptions and note limitation.', user, cfg);
    }

    async function callLocalChat(system, user, cfg){
        // Ollama and LM Studio both support OpenAI-compatible routes typically
        const base = cfg.baseUrl?.replace(/\/$/, '') || 'http://localhost:11434';
        const url = (cfg.provider === 'ollama') ? `${base}/v1/chat/completions` : `${base}/v1/chat/completions`;
        const body = { model: cfg.model || 'llama3.1', messages: [ {role:'system', content: system}, {role:'user', content: user} ] };
        const headers = { 'Content-Type': 'application/json' };
        if (cfg.apiKey) headers['Authorization'] = `Bearer ${cfg.apiKey}`; // LMStudio may require none
        const res = await fetch(url, { method: 'POST', headers, body: JSON.stringify(body) });
        const j = await res.json();
        if (!res.ok) throw new Error(j.error?.message || 'Local LLM error');
        return j.choices?.[0]?.message?.content || '';
    }

    async function callLocalVision(system, user, file, cfg){
        // Many local models won't support image input in OpenAI format; provide graceful fallback
        return callLocalChat(system + ' Note: If vision is unsupported, answer generally and note limitation.', user, cfg);
    }

    // Utilities
    async function copyFrom(id){
        const el = document.getElementById(id);
        if (!el) return;
        const text = ('value' in el) ? el.value : (el.textContent || '');
        try {
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(text);
            } else {
                const ta = document.createElement('textarea');
                ta.value = text;
                ta.setAttribute('readonly','');
                ta.style.cssText = 'position:fixed;left:-9999px;top:-9999px;';
                document.body.appendChild(ta);
                ta.select();
                document.execCommand('copy');
                document.body.removeChild(ta);
            }
            showToast('Copied to clipboard');
        } catch(err) {
            console.warn('Copy failed', err);
            showToast('Copy failed');
        }
    }

    function showToast(message){
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--success-color, #22c55e);
            color: #fff;
            padding: .6rem .9rem;
            border-radius: .5rem;
            box-shadow: 0 8px 24px rgba(0,0,0,.2);
            z-index: 10000;
            opacity: 0;
            transform: translateY(8px);
            transition: opacity .25s ease, transform .25s ease;
        `;
        document.body.appendChild(toast);
        requestAnimationFrame(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateY(0)';
        });
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateY(8px)';
            setTimeout(() => toast.remove(), 250);
        }, 1600);
    }

    // Loading helpers
    function setDisabled(el, disabled){ if (!el) return; el.disabled = disabled; el.classList.toggle('is-loading', !!disabled); }

    function setImg2PromptLoading(loading){
        setDisabled(document.getElementById('img2prompt-generate'), loading);
        const out = document.getElementById('img2prompt-output');
        if (out) out.disabled = loading;
        if (out && loading) out.value = 'Generating...';
        if (out) out.placeholder = loading ? 'Generating…' : 'Generated prompt will appear here...';
        if (out) out.classList.toggle('glow-loading', loading);
    }

    function setMagicLoading(loading){
        setDisabled(document.getElementById('magic-generate'), loading);
        const out = document.getElementById('magic-output');
        if (out) out.disabled = loading;
        if (out && loading) out.value = 'Enhancing...';
        if (out) out.placeholder = loading ? 'Enhancing…' : 'Enhanced prompt will appear here...';
        if (out) out.classList.toggle('glow-loading', loading);
    }

    function setDescribeLoading(loading){
        setDisabled(document.getElementById('describe-generate'), loading);
        const out = document.getElementById('describe-output');
        if (out) out.disabled = loading;
        if (out && loading) out.value = 'Generating...';
        if (out) out.placeholder = loading ? 'Analyzing…' : 'Result will appear here...';
        if (out) out.classList.toggle('glow-loading', loading);
    }

    function fileToBase64(file){
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }
})();

