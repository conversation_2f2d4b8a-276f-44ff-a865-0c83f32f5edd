/* --- Aspect Ratio Calculator Styles --- */
.tool-section {
    background-color: var(--surface-color);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.tool-section h3 {
    margin-bottom: 1.5rem;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
}

.calculator-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-color);
}

.form-group.button-group {
    justify-content: flex-end;
}

input[type="number"], select {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s;
}

input[type="number"]:focus, select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.2);
}

.result-box {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: var(--bg-color);
    border-radius: 8px;
    text-align: center;
    font-size: 1.1rem;
    color: var(--subtle-text-color);
    transition: all 0.3s;
}

.result-box.success {
    color: var(--success-color);
    font-weight: 500;
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
    .calculator-grid {
        grid-template-columns: 1fr;
    }
}
